{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/transpilers/types.ts"], "names": [], "mappings": "", "sourcesContent": ["import type * as ts from 'typescript';\nimport type { NodeModuleEmitKind, Service } from '../index';\nimport type { ProjectLocalResolveHelper } from '../util';\n\n/**\n * Third-party transpilers are implemented as a CommonJS module with a\n * named export \"create\"\n *\n * @category Transpiler\n */\nexport interface TranspilerModule {\n  create: TranspilerFactory;\n}\n/**\n * Called by ts-node to create a custom transpiler.\n *\n * @category Transpiler\n */\nexport type TranspilerFactory = (\n  options: CreateTranspilerOptions\n) => Transpiler;\n/** @category Transpiler */\nexport interface CreateTranspilerOptions {\n  // TODO this is confusing because its only a partial Service.  Rename?\n  // Careful: must avoid stripInternal breakage by guarding with Extract<>\n  service: Pick<\n    Service,\n    Extract<'config' | 'options' | 'projectLocalResolveHelper', keyof Service>\n  >;\n  /**\n   * If `\"transpiler\"` option is declared in an \"extends\" tsconfig, this path might be different than\n   * the `projectLocalResolveHelper`\n   *\n   * @internal\n   */\n  transpilerConfigLocalResolveHelper: ProjectLocalResolveHelper;\n  /**\n   * When using `module: nodenext` or `module: node12`, there are two possible styles of emit:\n   * - CommonJS with dynamic imports preserved (not transformed into `require()` calls)\n   * - ECMAScript modules with `import foo = require()` transformed into `require = createRequire(); const foo = require()`\n   * @internal\n   */\n  nodeModuleEmitKind?: NodeModuleEmitKind;\n}\n/** @category Transpiler */\nexport interface Transpiler {\n  // TODOs\n  // Create spec for returning diagnostics?  Currently transpilers are allowed to\n  // throw an error but that's it.\n  transpile(input: string, options: TranspileOptions): TranspileOutput;\n}\n/** @category Transpiler */\nexport interface TranspileOptions {\n  fileName: string;\n}\n/** @category Transpiler */\nexport interface TranspileOutput {\n  outputText: string;\n  diagnostics?: ts.Diagnostic[];\n  sourceMapText?: string;\n}\n"]}