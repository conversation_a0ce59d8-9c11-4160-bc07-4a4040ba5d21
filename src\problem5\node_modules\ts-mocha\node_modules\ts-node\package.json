{"name": "ts-node", "version": "7.0.1", "description": "TypeScript execution environment and REPL for node.js, with source map support", "main": "dist/index.js", "types": "dist/index.d.ts", "bin": {"ts-node": "dist/bin.js"}, "files": ["dist/", "register/", "LICENSE"], "scripts": {"lint": "tslint \"src/**/*.ts\" --project tsconfig.json", "clean": "<PERSON><PERSON><PERSON> dist", "tsc": "tsc", "build": "npm run clean && npm run tsc", "test-spec": "mocha dist/**/*.spec.js -R spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- \"dist/**/*.spec.js\" -R spec --bail", "test": "npm run build && npm run lint && npm run test-cov", "prepublish": "npm run build"}, "engines": {"node": ">=4.2.0"}, "repository": {"type": "git", "url": "git://github.com/TypeStrong/ts-node.git"}, "keywords": ["typescript", "node", "runtime", "environment", "ts", "compiler"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blakeembrey.me"}, "license": "MIT", "bugs": {"url": "https://github.com/TypeStrong/ts-node/issues"}, "homepage": "https://github.com/TypeStrong/ts-node", "devDependencies": {"@types/arrify": "^1.0.1", "@types/buffer-from": "^1.1.0", "@types/chai": "^4.0.4", "@types/diff": "^3.2.1", "@types/minimist": "^1.2.0", "@types/mkdirp": "^0.5.0", "@types/mocha": "^5.0.0", "@types/node": "^10.0.3", "@types/proxyquire": "^1.3.28", "@types/react": "^16.0.2", "@types/semver": "^5.3.34", "@types/source-map-support": "^0.4.0", "@types/yn": "types/npm-yn#ca75f6c82940fae6a06fb41d2d37a6aa9b4ea8e9", "chai": "^4.0.1", "istanbul": "^0.4.0", "mocha": "^5.0.1", "ntypescript": "^1.201507091536.1", "proxyquire": "^2.0.0", "react": "^16.0.0", "rimraf": "^2.5.4", "semver": "^5.1.0", "tslint": "^5.0.0", "tslint-config-standard": "^7.0.0", "typescript": "^2.8.3"}, "dependencies": {"arrify": "^1.0.0", "buffer-from": "^1.1.0", "diff": "^3.1.0", "make-error": "^1.1.1", "minimist": "^1.2.0", "mkdirp": "^0.5.1", "source-map-support": "^0.5.6", "yn": "^2.0.0"}}