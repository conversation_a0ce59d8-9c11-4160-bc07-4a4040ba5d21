{"version": 3, "file": "bin.js", "sourceRoot": "", "sources": ["../src/bin.ts"], "names": [], "mappings": ";;;AAEA,6BAAoC;AACpC,6BAAyC;AACzC,6BAA8B;AAC9B,+BAAiC;AACjC,+BAAiC;AACjC,mCAAqC;AACrC,6BAAgC;AAChC,yBAA2B;AAC3B,yBAA2C;AAC3C,iCAAqE;AA2BrE,IAAM,IAAI,GAAG,QAAQ,CAAO,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;IACjD,SAAS,EAAE,IAAI;IACf,MAAM,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,mBAAmB,EAAE,SAAS,EAAE,gBAAgB,EAAE,QAAQ,CAAC;IAC5G,OAAO,EAAE,CAAC,MAAM,EAAE,eAAe,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,aAAa,EAAE,YAAY,CAAC;IACnH,KAAK,EAAE;QACL,IAAI,EAAE,CAAC,GAAG,CAAC;QACX,KAAK,EAAE,CAAC,GAAG,CAAC;QACZ,OAAO,EAAE,CAAC,GAAG,CAAC;QACd,IAAI,EAAE,CAAC,GAAG,CAAC;QACX,OAAO,EAAE,CAAC,GAAG,CAAC;QACd,SAAS,EAAE,CAAC,YAAY,CAAC;QACzB,aAAa,EAAE,CAAC,GAAG,EAAE,gBAAgB,CAAC;QACtC,cAAc,EAAE,CAAC,iBAAiB,CAAC;QACnC,MAAM,EAAE,CAAC,GAAG,CAAC;QACb,OAAO,EAAE,CAAC,GAAG,CAAC;QACd,UAAU,EAAE,CAAC,aAAa,CAAC;QAC3B,WAAW,EAAE,CAAC,cAAc,CAAC;QAC7B,QAAQ,EAAE,CAAC,GAAG,CAAC;QACf,iBAAiB,EAAE,CAAC,GAAG,EAAE,oBAAoB,CAAC;QAC9C,eAAe,EAAE,CAAC,GAAG,EAAE,kBAAkB,CAAC;KAC3C;IACD,OAAO,EAAE;QACP,KAAK,EAAE,gBAAQ,CAAC,KAAK;QACrB,KAAK,EAAE,gBAAQ,CAAC,KAAK;QACrB,MAAM,EAAE,gBAAQ,CAAC,MAAM;QACvB,SAAS,EAAE,gBAAQ,CAAC,SAAS;QAC7B,aAAa,EAAE,gBAAQ,CAAC,aAAa;QACrC,cAAc,EAAE,gBAAQ,CAAC,cAAc;QACvC,MAAM,EAAE,gBAAQ,CAAC,MAAM;QACvB,OAAO,EAAE,gBAAQ,CAAC,OAAO;QACzB,UAAU,EAAE,gBAAQ,CAAC,UAAU;QAC/B,WAAW,EAAE,gBAAQ,CAAC,WAAW;QACjC,QAAQ,EAAE,gBAAQ,CAAC,QAAQ;QAC3B,iBAAiB,EAAE,gBAAQ,CAAC,iBAAiB;KAC9C;CACF,CAAC,CAAA;AAEF,IAAI,IAAI,CAAC,IAAI,EAAE;IACb,OAAO,CAAC,GAAG,CAAC,4uCAyBb,CAAC,CAAA;IAEA,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;CAChB;AAED,IAAM,GAAG,GAAG,OAAO,CAAC,GAAG,EAAE,CAAA;AACzB,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAA;AAC7D,IAAM,MAAM,GAAG,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAA,CAAC,yCAAyC;AACtG,IAAM,SAAS,GAAG,IAAI,CAAC,KAAK,KAAK,SAAS,CAAA;AAE1C,6CAA6C;AAC7C,IAAM,OAAO,GAAG,gBAAQ,CAAC;IACvB,KAAK,EAAE,IAAI,CAAC,KAAK;IACjB,MAAM,EAAE,IAAI,CAAC,MAAM;IACnB,SAAS,EAAE,IAAI,CAAC,SAAS;IACzB,aAAa,EAAE,IAAI,CAAC,aAAa;IACjC,KAAK,EAAE,IAAI,CAAC,KAAK;IACjB,cAAc,EAAE,IAAI,CAAC,cAAc;IACnC,MAAM,EAAE,IAAI,CAAC,MAAM;IACnB,OAAO,EAAE,IAAI,CAAC,OAAO;IACrB,UAAU,EAAE,IAAI,CAAC,UAAU;IAC3B,WAAW,EAAE,IAAI,CAAC,WAAW;IAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ;IACvB,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;IACzC,eAAe,EAAE,aAAK,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,gBAAQ,CAAC,eAAe;IACxE,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS;IAC3C,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,SAAS;CAChD,CAAC,CAAA;AAEF,8BAA8B;AAC9B,IAAI,IAAI,CAAC,OAAO,EAAE;IAChB,OAAO,CAAC,GAAG,CAAC,cAAY,eAAS,CAAC,CAAA;IAClC,OAAO,CAAC,GAAG,CAAC,UAAQ,OAAO,CAAC,OAAS,CAAC,CAAA;IACtC,OAAO,CAAC,GAAG,CAAC,iBAAe,OAAO,CAAC,EAAE,CAAC,OAAS,CAAC,CAAA;IAChD,OAAO,CAAC,GAAG,CAAC,WAAS,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAG,CAAC,CAAA;IACxD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;CAChB;AAED,6CAA6C;AAC5C,MAAc,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAA;AAErD;;GAEG;AACH,IAAM,aAAa,GAAG,WAAW,CAAA;AACjC,IAAM,SAAS,GAAG,WAAI,CAAC,GAAG,EAAE,aAAa,CAAC,CAAA;AAC1C,IAAM,aAAa,GAAG,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAA;AAErE,4DAA4D;AAC5D,IAAI,MAAM,EAAE;IACV,WAAW,CAAC,IAAc,EAAE,SAAS,CAAC,CAAA;CACvC;KAAM;IACL,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE;QACjB,OAAO,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,cAAO,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;QAC/E,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA;QACpC,MAAM,CAAC,OAAO,EAAE,CAAA;KACjB;SAAM;QACL,uEAAuE;QACvE,IAAK,OAAO,CAAC,KAAa,CAAC,KAAK,EAAE;YAChC,SAAS,EAAE,CAAA;SACZ;aAAM;YACL,IAAI,MAAI,GAAG,EAAE,CAAA;YACb,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM,EAAE,UAAC,KAAa,IAAK,OAAA,MAAI,IAAI,KAAK,EAAb,CAAa,CAAC,CAAA;YAC1D,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE,cAAM,OAAA,WAAW,CAAC,MAAI,EAAE,SAAS,CAAC,EAA5B,CAA4B,CAAC,CAAA;SAC5D;KACF;CACF;AAED;;GAEG;AACH,qBAAsB,IAAY,EAAE,SAAkB;IACpD,IAAM,MAAM,GAAG,IAAI,MAAM,CAAC,aAAa,CAAC,CAAA;IACxC,MAAM,CAAC,QAAQ,GAAG,aAAa,CAAA;IAC/B,MAAM,CAAC,KAAK,GAAI,MAAc,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAEnD;IAAC,MAAc,CAAC,UAAU,GAAG,aAAa,CAC1C;IAAC,MAAc,CAAC,SAAS,GAAG,GAAG,CAC/B;IAAC,MAAc,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CACxC;IAAC,MAAc,CAAC,MAAM,GAAG,MAAM,CAC/B;IAAC,MAAc,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;IAEtD,IAAI,MAAW,CAAA;IAEf,IAAI;QACF,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,CAAA;KACrB;IAAC,OAAO,KAAK,EAAE;QACd,IAAI,KAAK,YAAY,eAAO,EAAE;YAC5B,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC,CAAA;YACnC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;SAChB;QAED,MAAM,KAAK,CAAA;KACZ;IAED,IAAI,SAAS,EAAE;QACb,OAAO,CAAC,GAAG,CAAC,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,cAAO,CAAC,MAAM,CAAC,CAAC,CAAA;KACnE;AACH,CAAC;AAED;;GAEG;AACH,eAAgB,KAAa;IAC3B,IAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAA;IACjC,IAAM,YAAY,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;IACvC,IAAM,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC,CAAA;IAC9B,IAAI,MAAc,CAAA;IAElB,IAAI;QACF,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,EAAE,SAAS,EAAE,CAAC,KAAK,CAAC,CAAA;KACjE;IAAC,OAAO,GAAG,EAAE;QACZ,IAAI,EAAE,CAAA;QACN,MAAM,GAAG,CAAA;KACV;IAED,qDAAqD;IACrD,IAAM,OAAO,GAAG,gBAAS,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;IAEvD,IAAI,YAAY,EAAE;QAChB,IAAI,EAAE,CAAA;KACP;SAAM;QACL,aAAa,CAAC,MAAM,GAAG,MAAM,CAAA;KAC9B;IAED,OAAO,OAAO,CAAC,MAAM,CAAC,UAAC,MAAM,EAAE,MAAM;QACnC,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,MAAM,CAAA;IAClE,CAAC,EAAE,SAAS,CAAC,CAAA;AACf,CAAC;AAED;;GAEG;AACH,cAAe,IAAY,EAAE,QAAgB;IAC3C,IAAM,MAAM,GAAG,IAAI,WAAM,CAAC,IAAI,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAA;IAEvD,OAAO,MAAM,CAAC,gBAAgB,EAAE,CAAA;AAClC,CAAC;AAED;;GAEG;AACH;IACE,IAAM,IAAI,GAAG,YAAK,CAAC;QACjB,MAAM,EAAE,IAAI;QACZ,KAAK,EAAE,OAAO,CAAC,KAAK;QACpB,MAAM,EAAE,OAAO,CAAC,MAAM;QACtB,QAAQ,EAAE,OAAO,CAAC,MAAM,CAAC,KAAK;QAC9B,IAAI,EAAE,QAAQ;QACd,SAAS,EAAE,IAAI;KAChB,CAAC,CAAA;IAEF,2DAA2D;IAC3D,IAAM,SAAS,GAAG,UAAU,CAAC,EAAE,CAAC,CAAA;IAEhC;QACE,SAAS,EAAE,CAAA;QAEX,yEAAyE;QACzE,IAAI,CAAC,0BAA0B,EAAE,aAAa,CAAC,CAAA;IACjD,CAAC;IAED,KAAK,EAAE,CAAA;IACP,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;IAEvB,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;QACzB,IAAI,EAAE,2CAA2C;QACjD,MAAM,EAAE,UAAU,UAAkB;YAClC,IAAI,CAAC,UAAU,EAAE;gBACf,IAAI,CAAC,aAAa,EAAE,CAAA;gBACpB,OAAM;aACP;YAED,IAAM,IAAI,GAAG,UAAU,CAAC,UAAU,CAAC,CAAA;YAC7B,IAAA,oFAAmG,EAAjG,cAAI,EAAE,oBAAO,CAAoF;YAEzG,IAAI,EAAE,CAAA;YAEN,IAAI,CAAC,YAAY,CAAC,KAAK,CAAI,IAAI,WAAK,OAAO,CAAC,CAAC,CAAI,OAAO,OAAI,CAAC,CAAC,CAAC,EAAE,CAAE,CAAC,CAAA;YACpE,IAAI,CAAC,aAAa,EAAE,CAAA;QACtB,CAAC;KACF,CAAC,CAAA;AACJ,CAAC;AAED;;GAEG;AACH,kBAAmB,IAAY,EAAE,QAAa,EAAE,SAAiB,EAAE,QAA4C;IAC7G,IAAI,GAAsB,CAAA;IAC1B,IAAI,MAAW,CAAA;IAEf,kDAAkD;IAClD,IAAI,IAAI,KAAK,QAAQ,EAAE;QACrB,QAAQ,EAAE,CAAA;QACV,OAAM;KACP;IAED,IAAI;QACF,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,CAAA;KACrB;IAAC,OAAO,KAAK,EAAE;QACd,IAAI,KAAK,YAAY,eAAO,EAAE;YAC5B,oDAAoD;YACpD,IAAI,kBAAW,IAAI,aAAa,CAAC,KAAK,CAAC,EAAE;gBACvC,GAAG,GAAG,IAAI,kBAAW,CAAC,KAAK,CAAC,CAAA;aAC7B;iBAAM;gBACL,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC,CAAA;gBACnC,GAAG,GAAG,SAAS,CAAA;aAChB;SACF;aAAM;YACL,GAAG,GAAG,KAAK,CAAA;SACZ;KACF;IAED,QAAQ,CAAC,GAAG,EAAE,MAAM,CAAC,CAAA;AACvB,CAAC;AAED;;GAEG;AACH,oBAAqB,KAAa;IAChC,IAAM,SAAS,GAAG,aAAa,CAAC,KAAK,CAAA;IACrC,IAAM,WAAW,GAAG,aAAa,CAAC,OAAO,CAAA;IACzC,IAAM,UAAU,GAAG,aAAa,CAAC,MAAM,CAAA;IACvC,IAAM,SAAS,GAAG,aAAa,CAAC,KAAK,CAAA;IAErC,mDAAmD;IACnD,IAAI,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,IAAI,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;QAC7G,aAAa,CAAC,KAAK,GAAM,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAK,CAAA;KAC/D;IAED,aAAa,CAAC,KAAK,IAAI,KAAK,CAAA;IAC5B,aAAa,CAAC,KAAK,IAAI,SAAS,CAAC,KAAK,CAAC,CAAA;IACvC,aAAa,CAAC,OAAO,EAAE,CAAA;IAEvB,OAAO;QACL,aAAa,CAAC,KAAK,GAAG,SAAS,CAAA;QAC/B,aAAa,CAAC,MAAM,GAAG,UAAU,CAAA;QACjC,aAAa,CAAC,OAAO,GAAG,WAAW,CAAA;QACnC,aAAa,CAAC,KAAK,GAAG,SAAS,CAAA;IACjC,CAAC,CAAA;AACH,CAAC;AAED;;GAEG;AACH,mBAAoB,KAAa;IAC/B,IAAI,KAAK,GAAG,CAAC,CAAA;IAEb,KAAmB,UAAK,EAAL,eAAK,EAAL,mBAAK,EAAL,IAAK,EAAE;QAArB,IAAM,IAAI,cAAA;QACb,IAAI,IAAI,KAAK,IAAI,EAAE;YACjB,KAAK,EAAE,CAAA;SACR;KACF;IAED,OAAO,KAAK,CAAA;AACd,CAAC;AAED;;GAEG;AACH,sBAAuB,IAAY;IACjC,IAAI,IAAI,KAAK,SAAS;QAAE,OAAO,aAAa,CAAC,KAAK,CAAA;IAElD,IAAI;QACF,OAAO,iBAAY,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;KAClC;IAAC,OAAO,GAAG,EAAE,EAAC,aAAa,EAAC;AAC/B,CAAC;AAED;;GAEG;AACH,wBAAyB,IAAY;IACnC,IAAI,IAAI,KAAK,SAAS;QAAE,OAAO,IAAI,CAAA;IAEnC,IAAI;QACF,IAAM,KAAK,GAAG,aAAQ,CAAC,IAAI,CAAC,CAAA;QAC5B,OAAO,KAAK,CAAC,MAAM,EAAE,IAAI,KAAK,CAAC,MAAM,EAAE,CAAA;KACxC;IAAC,OAAO,GAAG,EAAE;QACZ,OAAO,KAAK,CAAA;KACb;AACH,CAAC;AAED,IAAM,cAAc,GAAgB,IAAI,GAAG,CAAC;IAC1C,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI,CAAC,oFAAoF;CAC1F,CAAC,CAAA;AAEF;;GAEG;AACH,uBAAwB,KAAc;IACpC,OAAO,KAAK,CAAC,eAAe,CAAC,KAAK,CAAC,UAAA,IAAI,IAAI,OAAA,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,EAAxB,CAAwB,CAAC,CAAA;AACtE,CAAC", "sourcesContent": ["#!/usr/bin/env node\n\nimport { join, resolve } from 'path'\nimport { start, Recoverable } from 'repl'\nimport { inspect } from 'util'\nimport arrify = require('arrify')\nimport Module = require('module')\nimport minimist = require('minimist')\nimport { diffLines } from 'diff'\nimport { Script } from 'vm'\nimport { readFileSync, statSync } from 'fs'\nimport { register, VERSION, DEFAULTS, TSError, parse } from './index'\n\ninterface Argv {\n  // Node.js-like options.\n  eval?: string\n  print?: string\n  require?: string | string[]\n  // CLI options.\n  help?: boolean\n  version?: boolean\n  // Register options.\n  pretty?: boolean\n  typeCheck?: boolean\n  transpileOnly?: boolean\n  files?: boolean\n  cache?: boolean\n  cacheDirectory?: string\n  compiler?: string\n  ignore?: string | string[]\n  project?: string\n  skipIgnore?: boolean\n  skipProject?: boolean\n  ignoreDiagnostics?: string | string[]\n  compilerOptions?: string\n  _: string[]\n}\n\nconst argv = minimist<Argv>(process.argv.slice(2), {\n  stopEarly: true,\n  string: ['eval', 'print', 'compiler', 'project', 'ignoreDiagnostics', 'require', 'cacheDirectory', 'ignore'],\n  boolean: ['help', 'transpileOnly', 'typeCheck', 'version', 'files', 'cache', 'pretty', 'skipProject', 'skipIgnore'],\n  alias: {\n    eval: ['e'],\n    print: ['p'],\n    require: ['r'],\n    help: ['h'],\n    version: ['v'],\n    typeCheck: ['type-check'],\n    transpileOnly: ['T', 'transpile-only'],\n    cacheDirectory: ['cache-directory'],\n    ignore: ['I'],\n    project: ['P'],\n    skipIgnore: ['skip-ignore'],\n    skipProject: ['skip-project'],\n    compiler: ['C'],\n    ignoreDiagnostics: ['D', 'ignore-diagnostics'],\n    compilerOptions: ['O', 'compiler-options']\n  },\n  default: {\n    cache: DEFAULTS.cache,\n    files: DEFAULTS.files,\n    pretty: DEFAULTS.pretty,\n    typeCheck: DEFAULTS.typeCheck,\n    transpileOnly: DEFAULTS.transpileOnly,\n    cacheDirectory: DEFAULTS.cacheDirectory,\n    ignore: DEFAULTS.ignore,\n    project: DEFAULTS.project,\n    skipIgnore: DEFAULTS.skipIgnore,\n    skipProject: DEFAULTS.skipProject,\n    compiler: DEFAULTS.compiler,\n    ignoreDiagnostics: DEFAULTS.ignoreDiagnostics\n  }\n})\n\nif (argv.help) {\n  console.log(`\nUsage: ts-node [options] [ -e script | script.ts ] [arguments]\n\nOptions:\n\n  -e, --eval [code]              Evaluate code\n  -p, --print [code]             Evaluate code and print result\n  -r, --require [path]           Require a node module before execution\n\n  -h, --help                     Print CLI usage\n  -v, --version                  Print module version information\n\n  -T, --transpile-only           Use TypeScript's faster \\`transpileModule\\`\n  --cache-directory              Configure the output file cache directory\n  -I, --ignore [pattern]         Override the path patterns to skip compilation\n  -P, --project [path]           Path to TypeScript JSON project file\n  -C, --compiler [name]          Specify a custom TypeScript compiler\n  -D, --ignoreDiagnostics [code] Ignore TypeScript warnings by diagnostic code\n  -O, --compilerOptions [opts]   JSON object to merge with compiler options\n\n  --files                        Load files from \\`tsconfig.json\\` on startup\n  --pretty                       Use pretty diagnostic formatter\n  --no-cache                     Disable the local TypeScript Node cache\n  --skip-project                 Skip reading \\`tsconfig.json\\`\n  --skip-ignore                  Skip \\`--ignore\\` checks\n`)\n\n  process.exit(0)\n}\n\nconst cwd = process.cwd()\nconst code = argv.eval === undefined ? argv.print : argv.eval\nconst isEval = typeof argv.eval === 'string' || !!argv.print // Minimist struggles with empty strings.\nconst isPrinted = argv.print !== undefined\n\n// Register the TypeScript compiler instance.\nconst service = register({\n  files: argv.files,\n  pretty: argv.pretty,\n  typeCheck: argv.typeCheck,\n  transpileOnly: argv.transpileOnly,\n  cache: argv.cache,\n  cacheDirectory: argv.cacheDirectory,\n  ignore: argv.ignore,\n  project: argv.project,\n  skipIgnore: argv.skipIgnore,\n  skipProject: argv.skipProject,\n  compiler: argv.compiler,\n  ignoreDiagnostics: argv.ignoreDiagnostics,\n  compilerOptions: parse(argv.compilerOptions) || DEFAULTS.compilerOptions,\n  readFile: isEval ? readFileEval : undefined,\n  fileExists: isEval ? fileExistsEval : undefined\n})\n\n// Output project information.\nif (argv.version) {\n  console.log(`ts-node v${VERSION}`)\n  console.log(`node ${process.version}`)\n  console.log(`typescript v${service.ts.version}`)\n  console.log(`cache ${JSON.stringify(service.cachedir)}`)\n  process.exit(0)\n}\n\n// Require specified modules before start-up.\n(Module as any)._preloadModules(arrify(argv.require))\n\n/**\n * Eval helpers.\n */\nconst EVAL_FILENAME = `[eval].ts`\nconst EVAL_PATH = join(cwd, EVAL_FILENAME)\nconst EVAL_INSTANCE = { input: '', output: '', version: 0, lines: 0 }\n\n// Execute the main contents (either eval, script or piped).\nif (isEval) {\n  evalAndExit(code as string, isPrinted)\n} else {\n  if (argv._.length) {\n    process.argv = ['node'].concat(resolve(cwd, argv._[0])).concat(argv._.slice(1))\n    process.execArgv.unshift(__filename)\n    Module.runMain()\n  } else {\n    // Piping of execution _only_ occurs when no other script is specified.\n    if ((process.stdin as any).isTTY) {\n      startRepl()\n    } else {\n      let code = ''\n      process.stdin.on('data', (chunk: Buffer) => code += chunk)\n      process.stdin.on('end', () => evalAndExit(code, isPrinted))\n    }\n  }\n}\n\n/**\n * Evaluate a script.\n */\nfunction evalAndExit (code: string, isPrinted: boolean) {\n  const module = new Module(EVAL_FILENAME)\n  module.filename = EVAL_FILENAME\n  module.paths = (Module as any)._nodeModulePaths(cwd)\n\n  ;(global as any).__filename = EVAL_FILENAME\n  ;(global as any).__dirname = cwd\n  ;(global as any).exports = module.exports\n  ;(global as any).module = module\n  ;(global as any).require = module.require.bind(module)\n\n  let result: any\n\n  try {\n    result = _eval(code)\n  } catch (error) {\n    if (error instanceof TSError) {\n      console.error(error.diagnosticText)\n      process.exit(1)\n    }\n\n    throw error\n  }\n\n  if (isPrinted) {\n    console.log(typeof result === 'string' ? result : inspect(result))\n  }\n}\n\n/**\n * Evaluate the code snippet.\n */\nfunction _eval (input: string) {\n  const lines = EVAL_INSTANCE.lines\n  const isCompletion = !/\\n$/.test(input)\n  const undo = appendEval(input)\n  let output: string\n\n  try {\n    output = service.compile(EVAL_INSTANCE.input, EVAL_PATH, -lines)\n  } catch (err) {\n    undo()\n    throw err\n  }\n\n  // Use `diff` to check for new JavaScript to execute.\n  const changes = diffLines(EVAL_INSTANCE.output, output)\n\n  if (isCompletion) {\n    undo()\n  } else {\n    EVAL_INSTANCE.output = output\n  }\n\n  return changes.reduce((result, change) => {\n    return change.added ? exec(change.value, EVAL_FILENAME) : result\n  }, undefined)\n}\n\n/**\n * Execute some code.\n */\nfunction exec (code: string, filename: string) {\n  const script = new Script(code, { filename: filename })\n\n  return script.runInThisContext()\n}\n\n/**\n * Start a CLI REPL.\n */\nfunction startRepl () {\n  const repl = start({\n    prompt: '> ',\n    input: process.stdin,\n    output: process.stdout,\n    terminal: process.stdout.isTTY,\n    eval: replEval,\n    useGlobal: true\n  })\n\n  // Bookmark the point where we should reset the REPL state.\n  const resetEval = appendEval('')\n\n  function reset () {\n    resetEval()\n\n    // Hard fix for TypeScript forcing `Object.defineProperty(exports, ...)`.\n    exec('exports = module.exports', EVAL_FILENAME)\n  }\n\n  reset()\n  repl.on('reset', reset)\n\n  repl.defineCommand('type', {\n    help: 'Check the type of a TypeScript identifier',\n    action: function (identifier: string) {\n      if (!identifier) {\n        repl.displayPrompt()\n        return\n      }\n\n      const undo = appendEval(identifier)\n      const { name, comment } = service.getTypeInfo(EVAL_INSTANCE.input, EVAL_PATH, EVAL_INSTANCE.input.length)\n\n      undo()\n\n      repl.outputStream.write(`${name}\\n${comment ? `${comment}\\n` : ''}`)\n      repl.displayPrompt()\n    }\n  })\n}\n\n/**\n * Eval code from the REPL.\n */\nfunction replEval (code: string, _context: any, _filename: string, callback: (err?: Error, result?: any) => any) {\n  let err: Error | undefined\n  let result: any\n\n  // TODO: Figure out how to handle completion here.\n  if (code === '.scope') {\n    callback()\n    return\n  }\n\n  try {\n    result = _eval(code)\n  } catch (error) {\n    if (error instanceof TSError) {\n      // Support recoverable compilations using >= node 6.\n      if (Recoverable && isRecoverable(error)) {\n        err = new Recoverable(error)\n      } else {\n        console.error(error.diagnosticText)\n        err = undefined\n      }\n    } else {\n      err = error\n    }\n  }\n\n  callback(err, result)\n}\n\n/**\n * Append to the eval instance and return an undo function.\n */\nfunction appendEval (input: string) {\n  const undoInput = EVAL_INSTANCE.input\n  const undoVersion = EVAL_INSTANCE.version\n  const undoOutput = EVAL_INSTANCE.output\n  const undoLines = EVAL_INSTANCE.lines\n\n  // Handle ASI issues with TypeScript re-evaluation.\n  if (undoInput.charAt(undoInput.length - 1) === '\\n' && /^\\s*[\\[\\(\\`]/.test(input) && !/;\\s*$/.test(undoInput)) {\n    EVAL_INSTANCE.input = `${EVAL_INSTANCE.input.slice(0, -1)};\\n`\n  }\n\n  EVAL_INSTANCE.input += input\n  EVAL_INSTANCE.lines += lineCount(input)\n  EVAL_INSTANCE.version++\n\n  return function () {\n    EVAL_INSTANCE.input = undoInput\n    EVAL_INSTANCE.output = undoOutput\n    EVAL_INSTANCE.version = undoVersion\n    EVAL_INSTANCE.lines = undoLines\n  }\n}\n\n/**\n * Count the number of lines.\n */\nfunction lineCount (value: string) {\n  let count = 0\n\n  for (const char of value) {\n    if (char === '\\n') {\n      count++\n    }\n  }\n\n  return count\n}\n\n/**\n * Get the file text, checking for eval first.\n */\nfunction readFileEval (path: string) {\n  if (path === EVAL_PATH) return EVAL_INSTANCE.input\n\n  try {\n    return readFileSync(path, 'utf8')\n  } catch (err) {/* Ignore. */}\n}\n\n/**\n * Get whether the file exists.\n */\nfunction fileExistsEval (path: string) {\n  if (path === EVAL_PATH) return true\n\n  try {\n    const stats = statSync(path)\n    return stats.isFile() || stats.isFIFO()\n  } catch (err) {\n    return false\n  }\n}\n\nconst RECOVERY_CODES: Set<number> = new Set([\n  1003, // \"Identifier expected.\"\n  1005, // \"')' expected.\"\n  1109, // \"Expression expected.\"\n  1126, // \"Unexpected end of text.\"\n  1160, // \"Unterminated template literal.\"\n  1161, // \"Unterminated regular expression literal.\"\n  2355 // \"A function whose declared type is neither 'void' nor 'any' must return a value.\"\n])\n\n/**\n * Check if a function can recover gracefully.\n */\nfunction isRecoverable (error: TSError) {\n  return error.diagnosticCodes.every(code => RECOVERY_CODES.has(code))\n}\n"]}