"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const resourceService_1 = __importDefault(require("../services/resourceService"));
class ResourceController {
    // GET /api/resources
    async getAllResources(req, res) {
        try {
            const filters = {
                category: req.query.category,
                status: req.query.status,
                search: req.query.search
            };
            const result = await resourceService_1.default.getAllResources(filters);
            if (!result.success) {
                res.status(500).json(result);
                return;
            }
            res.json({
                success: result.success,
                data: result.data,
                pagination: result.pagination
            });
        }
        catch (error) {
            res.status(500).json({
                success: false,
                error: 'Internal server error'
            });
        }
    }
    // GET /api/resources/:id
    async getResourceById(req, res) {
        try {
            const { id } = req.params;
            const result = await resourceService_1.default.getResourceById(id);
            if (!result.success) {
                res.status(404).json(result);
                return;
            }
            res.json(result);
        }
        catch (error) {
            res.status(500).json({
                success: false,
                error: 'Internal server error'
            });
        }
    }
    // POST /api/resources
    async createResource(req, res) {
        try {
            const resourceData = req.body;
            const result = await resourceService_1.default.createResource(resourceData);
            if (!result.success) {
                res.status(400).json(result);
                return;
            }
            res.status(201).json(result);
        }
        catch (error) {
            res.status(500).json({
                success: false,
                error: 'Internal server error'
            });
        }
    }
    // PUT /api/resources/:id
    async updateResource(req, res) {
        try {
            const { id } = req.params;
            const updateData = req.body;
            const result = await resourceService_1.default.updateResource(id, updateData);
            if (!result.success) {
                const statusCode = result.error?.includes('not found') ? 404 : 400;
                res.status(statusCode).json(result);
                return;
            }
            res.json(result);
        }
        catch (error) {
            res.status(500).json({
                success: false,
                error: 'Internal server error'
            });
        }
    }
    // PATCH /api/resources/:id
    async patchResource(req, res) {
        try {
            const { id } = req.params;
            const patchData = req.body;
            const result = await resourceService_1.default.patchResource(id, patchData);
            if (!result.success) {
                const statusCode = result.error?.includes('not found') ? 404 : 400;
                res.status(statusCode).json(result);
                return;
            }
            res.json(result);
        }
        catch (error) {
            res.status(500).json({
                success: false,
                error: 'Internal server error'
            });
        }
    }
    // DELETE /api/resources/:id
    async deleteResource(req, res) {
        try {
            const { id } = req.params;
            const result = await resourceService_1.default.deleteResource(id);
            if (!result.success) {
                res.status(404).json(result);
                return;
            }
            res.json(result);
        }
        catch (error) {
            res.status(500).json({
                success: false,
                error: 'Internal server error'
            });
        }
    }
}
exports.default = new ResourceController();
//# sourceMappingURL=resourceController.js.map