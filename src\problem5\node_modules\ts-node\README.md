<!--
BEFORE EDITING THIS README
Our README.md is auto-generated by combining pages in website/docs and website/readme-sources

If you are sending a pull request to improve documentation, submit your changes
in the source markdown files and we will generate the README from there.

You can build the readme with this command:

    cd website && yarn build-readme
-->

# [![TypeScript Node](logo.svg?sanitize=true)](https://typestrong.org/ts-node)

[![NPM version](https://img.shields.io/npm/v/ts-node.svg?style=flat)](https://npmjs.org/package/ts-node)
[![NPM downloads](https://img.shields.io/npm/dm/ts-node.svg?style=flat)](https://npmjs.org/package/ts-node)
[![Build status](https://img.shields.io/github/workflow/status/TypeStrong/ts-node/Continuous%20Integration)](https://github.com/TypeStrong/ts-node/actions?query=workflow%3A%22Continuous+Integration%22)
[![Test coverage](https://codecov.io/gh/TypeStrong/ts-node/branch/main/graph/badge.svg)](https://codecov.io/gh/TypeStrong/ts-node)

> TypeScript execution and REPL for node.js, with source map and native ESM support.

The latest documentation can also be found on our website: <https://typestrong.org/ts-node>

# Table of Contents

*   [Overview](#overview)
    *   [Features](#features)
*   [Installation](#installation)
*   [Usage](#usage)
    *   [Command Line](#command-line)
    *   [Shebang](#shebang)
    *   [node flags and other tools](#node-flags-and-other-tools)
    *   [Programmatic](#programmatic)
*   [Configuration](#configuration)
    *   [CLI flags](#cli-flags)
    *   [Via tsconfig.json (recommended)](#via-tsconfigjson-recommended)
        *   [@tsconfig/bases](#tsconfigbases)
        *   [Default config](#default-config)
    *   [`node` flags](#node-flags)
*   [Options](#options)
    *   [CLI Options](#cli-options)
        *   [help](#help)
        *   [version](#version)
        *   [eval](#eval)
        *   [print](#print)
        *   [interactive](#interactive)
        *   [esm](#esm)
    *   [TSConfig Options](#tsconfig-options)
        *   [project](#project)
        *   [skipProject](#skipproject)
        *   [cwdMode](#cwdmode)
        *   [compilerOptions](#compileroptions)
        *   [showConfig](#showconfig)
    *   [Typechecking](#typechecking)
        *   [transpileOnly](#transpileonly)
        *   [typeCheck](#typecheck)
        *   [compilerHost](#compilerhost)
        *   [files](#files)
        *   [ignoreDiagnostics](#ignorediagnostics)
    *   [Transpilation Options](#transpilation-options)
        *   [ignore](#ignore)
        *   [skipIgnore](#skipignore)
        *   [compiler](#compiler)
        *   [swc](#swc)
        *   [transpiler](#transpiler)
        *   [preferTsExts](#prefertsexts)
    *   [Diagnostic Options](#diagnostic-options)
        *   [logError](#logerror)
        *   [pretty](#pretty)
        *   [TS_NODE_DEBUG](#ts_node_debug)
    *   [Advanced Options](#advanced-options)
        *   [require](#require)
        *   [cwd](#cwd)
        *   [emit](#emit)
        *   [scope](#scope)
        *   [scopeDir](#scopedir)
        *   [moduleTypes](#moduletypes)
        *   [TS_NODE_HISTORY](#ts_node_history)
        *   [noExperimentalReplAwait](#noexperimentalreplawait)
        *   [experimentalResolver](#experimentalresolver)
        *   [experimentalSpecifierResolution](#experimentalspecifierresolution)
    *   [API Options](#api-options)
*   [SWC](#swc-1)
*   [CommonJS vs native ECMAScript modules](#commonjs-vs-native-ecmascript-modules)
    *   [CommonJS](#commonjs)
    *   [Native ECMAScript modules](#native-ecmascript-modules)
*   [Troubleshooting](#troubleshooting)
    *   [Configuration](#configuration-1)
    *   [Common errors](#common-errors)
        *   [`TSError`](#tserror)
        *   [`SyntaxError`](#syntaxerror)
            *   [Unsupported JavaScript syntax](#unsupported-javascript-syntax)
        *   [`ERR_REQUIRE_ESM`](#err_require_esm)
        *   [`ERR_UNKNOWN_FILE_EXTENSION`](#err_unknown_file_extension)
    *   [Missing Types](#missing-types)
    *   [npx, yarn dlx, and node_modules](#npx-yarn-dlx-and-node_modules)
*   [Performance](#performance)
    *   [Skip typechecking](#skip-typechecking)
    *   [With typechecking](#with-typechecking)
*   [Advanced](#advanced)
    *   [How it works](#how-it-works)
    *   [Ignored files](#ignored-files)
        *   [File extensions](#file-extensions)
        *   [Skipping `node_modules`](#skipping-node_modules)
        *   [Skipping pre-compiled TypeScript](#skipping-pre-compiled-typescript)
        *   [Scope by directory](#scope-by-directory)
        *   [Ignore by regexp](#ignore-by-regexp)
    *   [paths and baseUrl
        ](#paths-and-baseurl)
        *   [Why is this not built-in to ts-node?](#why-is-this-not-built-in-to-ts-node)
    *   [Third-party compilers](#third-party-compilers)
    *   [Transpilers](#transpilers)
        *   [Third-party plugins](#third-party-plugins)
        *   [Write your own plugin](#write-your-own-plugin)
    *   [Module type overrides](#module-type-overrides)
        *   [Caveats](#caveats)
    *   [API](#api)
*   [Recipes](#recipes)
    *   [Watching and restarting](#watching-and-restarting)
    *   [AVA](#ava)
        *   [CommonJS](#commonjs-1)
        *   [Native ECMAScript modules](#native-ecmascript-modules-1)
    *   [Gulp](#gulp)
    *   [IntelliJ and Webstorm](#intellij-and-webstorm)
    *   [Mocha](#mocha)
        *   [Mocha 7 and newer](#mocha-7-and-newer)
        *   [Mocha <=6](#mocha-6)
    *   [Tape](#tape)
    *   [Visual Studio Code](#visual-studio-code)
    *   [Other](#other)
*   [License](#license)

# Overview

ts-node is a TypeScript execution engine and REPL for Node.js.

It JIT transforms TypeScript into JavaScript, enabling you to directly execute TypeScript on Node.js without precompiling.
This is accomplished by hooking node's module loading APIs, enabling it to be used seamlessly alongside other Node.js
tools and libraries.

## Features

*   Automatic sourcemaps in stack traces
*   Automatic `tsconfig.json` parsing
*   Automatic defaults to match your node version
*   Typechecking (optional)
*   REPL
*   Write standalone scripts
*   Native ESM loader
*   Use third-party transpilers
*   Use custom transformers
*   Integrate with test runners, debuggers, and CLI tools
*   Compatible with pre-compilation for production

![TypeScript REPL](website/static/img/screenshot.png)

# Installation

```shell
# Locally in your project.
npm install -D typescript
npm install -D ts-node

# Or globally with TypeScript.
npm install -g typescript
npm install -g ts-node

# Depending on configuration, you may also need these
npm install -D tslib @types/node
```

**Tip:** Installing modules locally allows you to control and share the versions through `package.json`. ts-node will always resolve the compiler from `cwd` before checking relative to its own installation.

# Usage

## Command Line

```shell
# Execute a script as `node` + `tsc`.
ts-node script.ts

# Starts a TypeScript REPL.
ts-node

# Execute code with TypeScript.
ts-node -e 'console.log("Hello, world!")'

# Execute, and print, code with TypeScript.
ts-node -p -e '"Hello, world!"'

# Pipe scripts to execute with TypeScript.
echo 'console.log("Hello, world!")' | ts-node

# Equivalent to ts-node --transpileOnly
ts-node-transpile-only script.ts

# Equivalent to ts-node --cwdMode
ts-node-cwd script.ts

# Equivalent to ts-node --esm
ts-node-esm script.ts
```

## Shebang

To write scripts with maximum portability, [specify options in your `tsconfig.json`](#via-tsconfigjson-recommended) and omit them from the shebang.

```typescript twoslash
#!/usr/bin/env ts-node

// ts-node options are read from tsconfig.json

console.log("Hello, world!")
```

Including options within the shebang requires the [`env -S` flag](https://manpages.debian.org/bullseye/coreutils/env.1.en.html#S), which is available on recent versions of `env`. ([compatibility](https://github.com/TypeStrong/ts-node/pull/1448#issuecomment-913895766))

```typescript twoslash
#!/usr/bin/env -S ts-node --files
// This shebang works on Mac and Linux with newer versions of env
// Technically, Mac allows omitting `-S`, but Linux requires it
```

To test your version of `env` for compatibility with `-S`:

```shell
# Note that these unusual quotes are necessary
/usr/bin/env --debug '-S echo foo bar'
```

## node flags and other tools

You can register ts-node without using our CLI: `node -r ts-node/register` and `node --loader ts-node/esm`

In many cases, setting [`NODE_OPTIONS`](https://nodejs.org/api/cli.html#cli_node_options_options) will enable `ts-node` within other node tools, child processes, and worker threads.  This can be combined with other node flags.

```shell
NODE_OPTIONS="-r ts-node/register --no-warnings" node ./index.ts
```

Or, if you require native ESM support:

```shell
NODE_OPTIONS="--loader ts-node/esm"
```

This tells any node processes which receive this environment variable to install `ts-node`'s hooks before executing other code.

If you are invoking node directly, you can avoid the environment variable and pass those flags to node.

```shell
node --loader ts-node/esm --inspect ./index.ts
```

## Programmatic

You can require ts-node and register the loader for future requires by using `require('ts-node').register({ /* options */ })`.

Check out our [API](#api) for more features.

# Configuration

ts-node supports a variety of options which can be specified via `tsconfig.json`, as CLI flags, as environment variables, or programmatically.

For a complete list, see [Options](#options).

## CLI flags

ts-node CLI flags must come *before* the entrypoint script. For example:

```shell
$ ts-node --project tsconfig-dev.json say-hello.ts Ronald
Hello, Ronald!
```

## Via tsconfig.json (recommended)

ts-node automatically finds and loads `tsconfig.json`.  Most ts-node options can be specified in a `"ts-node"` object using their programmatic, camelCase names. We recommend this because it works even when you cannot pass CLI flags, such as `node --require ts-node/register` and when using shebangs.

Use `--skipProject` to skip loading the `tsconfig.json`.  Use `--project` to explicitly specify the path to a `tsconfig.json`.

When searching, it is resolved using [the same search behavior as `tsc`](https://www.typescriptlang.org/docs/handbook/tsconfig-json.html). By default, this search is performed relative to the entrypoint script. In `--cwdMode` or if no entrypoint is specified -- for example when using the REPL -- the search is performed relative to `--cwd` / `process.cwd()`.

You can use this sample configuration as a starting point:

```jsonc title="tsconfig.json"
{
  // This is an alias to @tsconfig/node16: https://github.com/tsconfig/bases
  "extends": "ts-node/node16/tsconfig.json",

  // Most ts-node options can be specified here using their programmatic names.
  "ts-node": {
    // It is faster to skip typechecking.
    // Remove if you want ts-node to do typechecking.
    "transpileOnly": true,

    "files": true,

    "compilerOptions": {
      // compilerOptions specified here will override those declared below,
      // but *only* in ts-node.  Useful if you want ts-node and tsc to use
      // different options with a single tsconfig.json.
    }
  },
  "compilerOptions": {
    // typescript options here
  }
}
```

Our bundled [JSON schema](https://unpkg.com/browse/ts-node@latest/tsconfig.schema.json) lists all compatible options.

### @tsconfig/bases

[@tsconfig/bases](https://github.com/tsconfig/bases) maintains recommended configurations for several node versions.
As a convenience, these are bundled with ts-node.

```jsonc title="tsconfig.json"
{
  "extends": "ts-node/node16/tsconfig.json",

  // Or install directly with `npm i -D @tsconfig/node16`
  "extends": "@tsconfig/node16/tsconfig.json",
}
```

### Default config

If no `tsconfig.json` is loaded from disk, ts-node will use the newest recommended defaults from
[@tsconfig/bases](https://github.com/tsconfig/bases/) compatible with your `node` and `typescript` versions.
With the latest `node` and `typescript`, this is [`@tsconfig/node16`](https://github.com/tsconfig/bases/blob/master/bases/node16.json).

Older versions of `typescript` are incompatible with `@tsconfig/node16`.  In those cases we will use an older default configuration.

When in doubt, `ts-node --showConfig` will log the configuration being used, and `ts-node -vv` will log `node` and `typescript` versions.

## `node` flags

[`node` flags](https://nodejs.org/api/cli.html) must be passed directly to `node`; they cannot be passed to the ts-node binary nor can they be specified in `tsconfig.json`

We recommend using the [`NODE_OPTIONS`](https://nodejs.org/api/cli.html#cli_node_options_options) environment variable to pass options to `node`.

```shell
NODE_OPTIONS='--trace-deprecation --abort-on-uncaught-exception' ts-node ./index.ts
```

Alternatively, you can invoke `node` directly and install ts-node via `--require`/`-r`

```shell
node --trace-deprecation --abort-on-uncaught-exception -r ts-node/register ./index.ts
```

# Options

All command-line flags support both `--camelCase` and `--hyphen-case`.

Most options can be declared in your tsconfig.json: [Configuration via tsconfig.json](#via-tsconfigjson-recommended)

`ts-node` supports `--print` (`-p`), `--eval` (`-e`), `--require` (`-r`) and `--interactive` (`-i`) similar to the [node.js CLI](https://nodejs.org/api/cli.html).

`ts-node` supports `--project` and `--showConfig` similar to the [tsc CLI](https://www.typescriptlang.org/docs/handbook/compiler-options.html#compiler-options).

*Environment variables, where available, are in `ALL_CAPS`*

## CLI Options

### help

```shell
ts-node --help
```

Prints the help text

### version

```shell
ts-node -v
ts-node -vvv
```

Prints the version. `-vv` includes node and typescript compiler versions.  `-vvv` includes absolute paths to ts-node and
typescript installations.

### eval

```shell
ts-node -e <typescript code>
# Example
ts-node -e 'console.log("Hello world!")'
```

Evaluate code

### print

```shell
ts-node -p -e <typescript code>
# Example
ts-node -p -e '"Hello world!"'
```

Print result of `--eval`

### interactive

```shell
ts-node -i
```

Opens the REPL even if stdin does not appear to be a terminal

### esm

```shell
ts-node --esm
ts-node-esm
```

Bootstrap with the ESM loader, enabling full ESM support

## TSConfig Options

### project

```shell
ts-node -P <path/to/tsconfig>
ts-node --project <path/to/tsconfig>
```

Path to tsconfig file.

*Note the uppercase `-P`. This is different from `tsc`'s `-p/--project` option.*

*Environment:* `TS_NODE_PROJECT`

### skipProject

```shell
ts-node --skipProject
```

Skip project config resolution and loading

*Default:* `false` <br/>
*Environment:* `TS_NODE_SKIP_PROJECT`

### cwdMode

```shell
ts-node -c
ts-node --cwdMode
ts-node-cwd
```

Resolve config relative to the current directory instead of the directory of the entrypoint script

### compilerOptions

```shell
ts-node -O <json compilerOptions>
ts-node --compilerOptions <json compilerOptions>
```

JSON object to merge with compiler options

*Environment:* `TS_NODE_COMPILER_OPTIONS`

### showConfig

```shell
ts-node --showConfig
```

Print resolved `tsconfig.json`, including `ts-node` options, and exit

## Typechecking

### transpileOnly

```shell
ts-node -T
ts-node --transpileOnly
```

Use TypeScript's faster `transpileModule`

*Default:* `false`<br/>
*Environment:* `TS_NODE_TRANSPILE_ONLY`

### typeCheck

```shell
ts-node --typeCheck
```

Opposite of `--transpileOnly`

*Default:* `true`<br/>
*Environment:* `TS_NODE_TYPE_CHECK`

### compilerHost

```shell
ts-node -H
ts-node --compilerHost
```

Use TypeScript's compiler host API

*Default:* `false` <br/>
*Environment:* `TS_NODE_COMPILER_HOST`

### files

```shell
ts-node --files
```

Load `files`, `include` and `exclude` from `tsconfig.json` on startup.  This may
avoid certain typechecking failures.  See [Missing types](#missing-types) for details.

*Default:* `false` <br/>
*Environment:* `TS_NODE_FILES`

### ignoreDiagnostics

```shell
ts-node -D <code,code>
ts-node --ignoreDiagnostics <code,code>
```

Ignore TypeScript warnings by diagnostic code

*Environment:* `TS_NODE_IGNORE_DIAGNOSTICS`

## Transpilation Options

### ignore

```shell
ts-node -I <regexp matching ignored files>
ts-node --ignore <regexp matching ignored files>
```

Override the path patterns to skip compilation

*Default:* `/node_modules/` <br/>
*Environment:* `TS_NODE_IGNORE`

### skipIgnore

```shell
ts-node --skipIgnore
```

Skip ignore checks

*Default:* `false` <br/>
*Environment:* `TS_NODE_SKIP_IGNORE`

### compiler

```shell
ts-node -C <name>
ts-node --compiler <name>
```

Specify a custom TypeScript compiler

*Default:* `typescript` <br/>
*Environment:* `TS_NODE_COMPILER`

### swc

```shell
ts-node --swc
```

Transpile with [swc](#swc).  Implies `--transpileOnly`

*Default:* `false`

### transpiler

```shell
ts-node --transpiler <name>
# Example
ts-node --transpiler ts-node/transpilers/swc
```

Use a third-party, non-typechecking transpiler

### preferTsExts

```shell
ts-node --preferTsExts
```

Re-order file extensions so that TypeScript imports are preferred

*Default:* `false` <br/>
*Environment:* `TS_NODE_PREFER_TS_EXTS`

## Diagnostic Options

### logError

```shell
ts-node --logError
```

Logs TypeScript errors to stderr instead of throwing exceptions

*Default:* `false` <br/>
*Environment:* `TS_NODE_LOG_ERROR`

### pretty

```shell
ts-node --pretty
```

Use pretty diagnostic formatter

*Default:* `false` <br/>
*Environment:* `TS_NODE_PRETTY`

### TS_NODE_DEBUG

```shell
TS_NODE_DEBUG=true ts-node
```

Enable debug logging

## Advanced Options

### require

```shell
ts-node -r <module name or path>
ts-node --require <module name or path>
```

Require a node module before execution

### cwd

```shell
ts-node --cwd <path/to/directory>
```

Behave as if invoked in this working directory

*Default:* `process.cwd()`<br/>
*Environment:* `TS_NODE_CWD`

### emit

```shell
ts-node --emit
```

Emit output files into `.ts-node` directory. Requires `--compilerHost`

*Default:* `false` <br/>
*Environment:* `TS_NODE_EMIT`

### scope

```shell
ts-node --scope
```

Scope compiler to files within `scopeDir`.  Anything outside this directory is ignored.

*Default:* `false` <br/>
*Environment:* `TS_NODE_SCOPE`

### scopeDir

```shell
ts-node --scopeDir <path/to/directory>
```

Directory within which compiler is limited when `scope` is enabled.

*Default:* First of: `tsconfig.json` "rootDir" if specified, directory containing `tsconfig.json`, or cwd if no `tsconfig.json` is loaded.<br/>
*Environment:* `TS_NODE_SCOPE_DIR`

### moduleTypes

Override the module type of certain files, ignoring the `package.json` `"type"` field.  See [Module type overrides](#module-type-overrides) for details.

*Default:* obeys `package.json` `"type"` and `tsconfig.json` `"module"` <br/>
*Can only be specified via `tsconfig.json` or API.*

### TS_NODE_HISTORY

```shell
TS_NODE_HISTORY=<path/to/history/file> ts-node
```

Path to history file for REPL

*Default:* `~/.ts_node_repl_history`

### noExperimentalReplAwait

```shell
ts-node --noExperimentalReplAwait
```

Disable top-level await in REPL.  Equivalent to node's [`--no-experimental-repl-await`](https://nodejs.org/api/cli.html#cli_no_experimental_repl_await)

*Default:* Enabled if TypeScript version is 3.8 or higher and target is ES2018 or higher.<br/>
*Environment:* `TS_NODE_EXPERIMENTAL_REPL_AWAIT` set `false` to disable

### experimentalResolver

Enable experimental hooks that re-map imports and require calls to support:

*   remapping extensions, e.g. so that `import "./foo.js"` will execute `foo.ts`. Currently the following extensions will be mapped:
    *   `.js` to `.ts`, `.tsx`, or `.jsx`
    *   `.cjs` to `.cts`
    *   `.mjs` to `.mts`
    *   `.jsx` to `.tsx`
*   including file extensions in CommonJS, for consistency with ESM where this is often mandatory

In the future, this hook will also support:

*   `baseUrl`, `paths`
*   `rootDirs`
*   `outDir` to `rootDir` mappings for composite projects and monorepos

For details, see [#1514](https://github.com/TypeStrong/ts-node/issues/1514).

*Default:* `false`, but will likely be enabled by default in a future version<br/>
*Can only be specified via `tsconfig.json` or API.*

### experimentalSpecifierResolution

```shell
ts-node --experimentalSpecifierResolution node
```

Like node's [`--experimental-specifier-resolution`](https://nodejs.org/dist/latest-v18.x/docs/api/esm.html#customizing-esm-specifier-resolution-algorithm), but can also be set in your `tsconfig.json` for convenience.
Requires [`esm`](#esm) to be enabled.

*Default:* `explicit`<br/>

## API Options

The API includes [additional options](https://typestrong.org/ts-node/api/interfaces/RegisterOptions.html) not shown here.

# SWC

SWC support is built-in via the `--swc` flag or `"swc": true` tsconfig option.

[SWC](https://swc.rs) is a TypeScript-compatible transpiler implemented in Rust.  This makes it an order of magnitude faster than vanilla `transpileOnly`.

To use it, first install `@swc/core` or `@swc/wasm`.  If using `importHelpers`, also install `@swc/helpers`.  If `target` is less than "es2015" and using `async`/`await` or generator functions, also install `regenerator-runtime`.

```shell
npm i -D @swc/core @swc/helpers regenerator-runtime
```

Then add the following to your `tsconfig.json`.

```jsonc title="tsconfig.json"
{
  "ts-node": {
    "swc": true
  }
}
```

> SWC uses `@swc/helpers` instead of `tslib`.  If you have enabled `importHelpers`, you must also install `@swc/helpers`.

# CommonJS vs native ECMAScript modules

TypeScript is almost always written using modern `import` syntax, but it is also transformed before being executed by the underlying runtime.  You can choose to either transform to CommonJS or to preserve the native `import` syntax, using node's native ESM support.  Configuration is different for each.

Here is a brief comparison of the two.

| CommonJS | Native ECMAScript modules |
|---|---|
| Write native `import` syntax | Write native `import` syntax |
| Transforms `import` into `require()` | Does not transform `import` |
| Node executes scripts using the classic [CommonJS loader](https://nodejs.org/dist/latest-v16.x/docs/api/modules.html) | Node executes scripts using the new [ESM loader](https://nodejs.org/dist/latest-v16.x/docs/api/esm.html) |
| Use any of:<br/>`ts-node`<br/>`node -r ts-node/register`<br/>`NODE_OPTIONS="ts-node/register" node`<br/>`require('ts-node').register({/* options */})` | Use any of:<br/>`ts-node --esm`<br/>`ts-node-esm`<br/>Set `"esm": true` in `tsconfig.json`<br />`node --loader ts-node/esm`<br/>`NODE_OPTIONS="--loader ts-node/esm" node` |

## CommonJS

Transforming to CommonJS is typically simpler and more widely supported because it is older.  You must remove [`"type": "module"`](https://nodejs.org/api/packages.html#packages_type) from `package.json` and set [`"module": "CommonJS"`](https://www.typescriptlang.org/tsconfig/#module) in `tsconfig.json`.

```jsonc title="package.json"
{
  // This can be omitted; commonjs is the default
  "type": "commonjs"
}
```

```jsonc title="tsconfig.json"
{
  "compilerOptions": {
    "module": "CommonJS"
  }
}
```

If you must keep `"module": "ESNext"` for `tsc`, webpack, or another build tool, you can set an override for ts-node.

```jsonc title="tsconfig.json"
{
  "compilerOptions": {
    "module": "ESNext"
  },
  "ts-node": {
    "compilerOptions": {
      "module": "CommonJS"
    }
  }
}
```

## Native ECMAScript modules

[Node's ESM loader hooks](https://nodejs.org/api/esm.html#esm_experimental_loaders) are [**experimental**](https://nodejs.org/api/documentation.html#documentation_stability_index) and subject to change. ts-node's ESM support is as stable as possible, but it relies on APIs which node can *and will* break in new versions of node.  Thus it is not recommended for production.

For complete usage, limitations, and to provide feedback, see [#1007](https://github.com/TypeStrong/ts-node/issues/1007).

You must set [`"type": "module"`](https://nodejs.org/api/packages.html#packages_type) in `package.json` and [`"module": "ESNext"`](https://www.typescriptlang.org/tsconfig/#module) in `tsconfig.json`.

```jsonc title="package.json"
{
  "type": "module"
}
```

```jsonc title="tsconfig.json"
{
  "compilerOptions": {
    "module": "ESNext" // or ES2015, ES2020
  },
  "ts-node": {
    // Tell ts-node CLI to install the --loader automatically, explained below
    "esm": true
  }
}
```

You must also ensure node is passed `--loader`.  The ts-node CLI will do this automatically with our `esm` option.

> Note: `--esm` must spawn a child process to pass it `--loader`.  This may change if node adds the ability to install loader hooks
> into the current process.

```shell
# pass the flag
ts-node --esm
# Use the convenience binary
ts-node-esm
# or add `"esm": true` to your tsconfig.json to make it automatic
ts-node
```

If you are not using our CLI, pass the loader flag to node.

```shell
node --loader ts-node/esm ./index.ts
# Or via environment variable
NODE_OPTIONS="--loader ts-node/esm" node ./index.ts
```

# Troubleshooting

## Configuration

ts-node uses sensible default configurations to reduce boilerplate while still respecting `tsconfig.json` if you
have one.  If you are unsure which configuration is used, you can log it with `ts-node --showConfig`.  This is similar to
`tsc --showConfig` but includes `"ts-node"` options as well.

ts-node also respects your locally-installed `typescript` version, but global installations fallback to the globally-installed
`typescript`.  If you are unsure which versions are used, `ts-node -vv` will log them.

```shell
$ ts-node -vv
ts-node v10.0.0
node v16.1.0
compiler v4.2.2

$ ts-node --showConfig
{
  "compilerOptions": {
    "target": "es6",
    "lib": [
      "es6",
      "dom"
    ],
    "rootDir": "./src",
    "outDir": "./.ts-node",
    "module": "commonjs",
    "moduleResolution": "node",
    "strict": true,
    "declaration": false,
    "sourceMap": true,
    "inlineSources": true,
    "types": [
      "node"
    ],
    "stripInternal": true,
    "incremental": true,
    "skipLibCheck": true,
    "importsNotUsedAsValues": "error",
    "inlineSourceMap": false,
    "noEmit": false
  },
  "ts-node": {
    "cwd": "/d/project",
    "projectSearchDir": "/d/project",
    "require": [],
    "project": "/d/project/tsconfig.json"
  }
}
```

## Common errors

It is important to differentiate between errors from ts-node, errors from the TypeScript compiler, and errors from `node`.  It is also important to understand when errors are caused by a type error in your code, a bug in your code, or a flaw in your configuration.

### `TSError`

Type errors from the compiler are thrown as a `TSError`.  These are the same as errors you get from `tsc`.

### `SyntaxError`

Any error that is not a `TSError` is from node.js (e.g. `SyntaxError`), and cannot be fixed by TypeScript or ts-node. These are bugs in your code or configuration.

#### Unsupported JavaScript syntax

Your version of `node` may not support all JavaScript syntax supported by TypeScript.  The compiler must transform this syntax via "downleveling," which is controlled by
the [tsconfig `"target"` option](https://www.typescriptlang.org/tsconfig#target).  Otherwise your code will compile fine, but node will throw a `SyntaxError`.

For example, `node` 12 does not understand the `?.` optional chaining operator.  If you use `"target": "esnext"`, then the following TypeScript syntax:

```typescript twoslash
const bar: string | undefined = foo?.bar;
```

will compile into this JavaScript:

```javascript
const a = foo?.bar;
```

When you try to run this code, node 12 will throw a `SyntaxError`.  To fix this, you must switch to `"target": "es2019"` or lower so TypeScript transforms `?.` into something `node` can understand.

### `ERR_REQUIRE_ESM`

This error is thrown by node when a module is `require()`d, but node believes it should execute as native ESM.  This can happen for a few reasons:

*   You have installed an ESM dependency but your own code compiles to CommonJS.
    *   Solution: configure your project to compile and execute as native ESM. [Docs](#native-ecmascript-modules)
    *   Solution: downgrade the dependency to an older, CommonJS version.
*   You have moved your project to ESM but still have a config file, such as `webpack.config.ts`, which must be executed as CommonJS <!-- SYNC_WITH_MTO_DOCS -->
    *   Solution: if supported by the relevant tool, rename your config file to `.cts`
    *   Solution: Configure a module type override. [Docs](#module-type-overrides)
*   You have a mix of CommonJS and native ESM in your project
    *   Solution: double-check all package.json "type" and tsconfig.json "module" configuration [Docs](#commonjs-vs-native-ecmascript-modules)
    *   Solution: consider simplifying by making your project entirely CommonJS or entirely native ESM

### `ERR_UNKNOWN_FILE_EXTENSION`

This error is thrown by node when a module has an unrecognized file extension, or no extension at all, and is being executed as native ESM.  This can happen for a few reasons:

*   You are using a tool which has an extensionless binary, such as `mocha`.
    *   CommonJS supports extensionless files but native ESM does not.
    *   Solution: upgrade to ts-node >=[v10.6.0](https://github.com/TypeStrong/ts-node/releases/tag/v10.6.0), which implements a workaround.
*   Our ESM loader is not installed.
    *   Solution: Use `ts-node-esm`, `ts-node --esm`, or add `"ts-node": {"esm": true}` to your tsconfig.json.  [Docs](#native-ecmascript-modules)
*   You have moved your project to ESM but still have a config file, such as `webpack.config.ts`, which must be executed as CommonJS <!-- SYNC_WITH_MTO_DOCS -->
    *   Solution: if supported by the relevant tool, rename your config file to `.cts`
    *   Solution: Configure a module type override. [Docs](#module-type-overrides)

## Missing Types

ts-node does *not* eagerly load `files`, `include` or `exclude` by default. This is because a large majority of projects do not use all of the files in a project directory (e.g. `Gulpfile.ts`, runtime vs tests) and parsing every file for types slows startup time. Instead, ts-node starts with the script file (e.g. `ts-node index.ts`) and TypeScript resolves dependencies based on imports and references.

Occasionally, this optimization leads to missing types. Fortunately, there are other ways to include them in typechecking.

For global definitions, you can use the `typeRoots` compiler option.  This requires that your type definitions be structured as type packages (not loose TypeScript definition files). More details on how this works can be found in the [TypeScript Handbook](https://www.typescriptlang.org/docs/handbook/tsconfig-json.html#types-typeroots-and-types).

Example `tsconfig.json`:

```jsonc
{
  "compilerOptions": {
    "typeRoots" : ["./node_modules/@types", "./typings"]
  }
}
```

Example project structure:

```text
<project_root>/
-- tsconfig.json
-- typings/
  -- <module_name>/
    -- index.d.ts
```

Example module declaration file:

```typescript twoslash
declare module '<module_name>' {
    // module definitions go here
}
```

For module definitions, you can use [`paths`](https://www.typescriptlang.org/docs/handbook/module-resolution.html#path-mapping):

```jsonc title="tsconfig.json"
{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "custom-module-type": ["types/custom-module-type"]
    }
  }
}
```

Another option is [triple-slash directives](https://www.typescriptlang.org/docs/handbook/triple-slash-directives.html). This may be helpful if you prefer not to change your `compilerOptions` or structure your type definitions for `typeRoots`. Below is an example of a triple-slash directive as a relative path within your project:

```typescript twoslash
/// <reference path="./types/lib_greeter" />
import {Greeter} from "lib_greeter"
const g = new Greeter();
g.sayHello();
```

If none of the above work, and you *must* use `files`, `include`, or `exclude`, enable our [`files`](#files) option.

## npx, yarn dlx, and node_modules

When executing TypeScript with `npx` or `yarn dlx`, the code resides within a temporary `node_modules` directory.

The contents of `node_modules` are ignored by default.  If execution fails, enable [`skipIgnore`](#skipignore).

<!--See also: [npx and yarn dlx](./recipes/npx-and-yarn-dlx.md)-->

# Performance

These tricks will make ts-node faster.

## Skip typechecking

It is often better to typecheck as part of your tests or linting.  You can run `tsc --noEmit` to do this.  In these cases, ts-node can skip typechecking, making it much faster.

To skip typechecking in ts-node, do one of the following:

*   Enable [swc](#swc)
    *   This is by far the fastest option
*   Enable [`transpileOnly`](#transpileonly) to skip typechecking without swc

## With typechecking

If you absolutely must typecheck in ts-node:

*   Avoid dynamic `require()` which may trigger repeated typechecking; prefer `import`
*   Try with and without `--files`; one may be faster depending on your project
*   Check `tsc --showConfig`; make sure all executed files are included
*   Enable [`skipLibCheck`](https://www.typescriptlang.org/tsconfig#skipLibCheck)
*   Set a [`types`](https://www.typescriptlang.org/tsconfig#types) array to avoid loading unnecessary `@types`

# Advanced

## How it works

ts-node works by registering hooks for `.ts`, `.tsx`, `.js`, and/or `.jsx` extensions.

Vanilla `node` loads `.js` by reading code from disk and executing it.  Our hook runs in the middle, transforming code from TypeScript to JavaScript and passing the result to `node` for execution.  This transformation will respect your `tsconfig.json` as if you had compiled via `tsc`.

We also register a few other hooks to apply sourcemaps to stack traces and remap from `.js` imports to `.ts`.

## Ignored files

ts-node transforms certain files and ignores others.  We refer to this mechanism as "scoping."  There are various
options to configure scoping, so that ts-node transforms only the files in your project.

> **Warning:**
>
> An ignored file can still be executed by node.js.  Ignoring a file means we do not transform it from TypeScript into JavaScript, but it does not prevent execution.
>
> If a file requires transformation but is ignored, node may either fail to resolve it or attempt to execute it as vanilla JavaScript.  This may cause syntax errors or other failures, because node does not understand TypeScript type syntax nor bleeding-edge ECMAScript features.

### File extensions

`.js` and `.jsx` are only transformed when [`allowJs`](https://www.typescriptlang.org/docs/handbook/compiler-options.html#compiler-options) is enabled.

`.tsx` and `.jsx` are only transformed when [`jsx`](https://www.typescriptlang.org/docs/handbook/jsx.html) is enabled.

> **Warning:**
>
> When ts-node is used with `allowJs`, *all* non-ignored JavaScript files are transformed by ts-node.

### Skipping `node_modules`

By default, ts-node avoids compiling files in `/node_modules/` for three reasons:

1.  Modules should always be published in a format node.js can consume
2.  Transpiling the entire dependency tree will make your project slower
3.  Differing behaviours between TypeScript and node.js (e.g. ES2015 modules) can result in a project that works until you decide to support a feature natively from node.js

If you need to import uncompiled TypeScript in `node_modules`, use [`--skipIgnore`](#skipignore) or [`TS_NODE_SKIP_IGNORE`](#skipignore) to bypass this restriction.

### Skipping pre-compiled TypeScript

If a compiled JavaScript file with the same name as a TypeScript file already exists, the TypeScript file will be ignored.  ts-node will import the pre-compiled JavaScript.

To force ts-node to import the TypeScript source, not the precompiled JavaScript, use [`--preferTsExts`](#prefertsexts).

### Scope by directory

Our [`scope`](#scope) and [`scopeDir`](#scopedir) options will limit transformation to files
within a directory.

### Ignore by regexp

Our [`ignore`](#ignore) option will ignore files matching one or more regular expressions.

## paths and baseUrl&#xA;

You can use ts-node together with [tsconfig-paths](https://www.npmjs.com/package/tsconfig-paths) to load modules according to the `paths` section in `tsconfig.json`.

```jsonc title="tsconfig.json"
{
  "ts-node": {
    // Do not forget to `npm i -D tsconfig-paths`
    "require": ["tsconfig-paths/register"]
  }
}
```

### Why is this not built-in to ts-node?

The official TypeScript Handbook explains the intended purpose for `"paths"` in ["Additional module resolution flags"](https://www.typescriptlang.org/docs/handbook/module-resolution.html#additional-module-resolution-flags).

> The TypeScript compiler has a set of additional flags to *inform* the compiler of transformations that are expected to happen to the sources to generate the final output.
>
> It is important to note that the compiler will not perform any of these transformations; it just uses these pieces of information to guide the process of resolving a module import to its definition file.

This means `"paths"` are intended to describe mappings that the build tool or runtime *already* performs, not to tell the build tool or
runtime how to resolve modules.  In other words, they intend us to write our imports in a way `node` already understands.  For this reason, ts-node does not modify `node`'s module resolution behavior to implement `"paths"` mappings.

## Third-party compilers

Some projects require a patched typescript compiler which adds additional features.  For example, [`ttypescript`](https://github.com/cevek/ttypescript/tree/master/packages/ttypescript) and [`ts-patch`](https://github.com/nonara/ts-patch#readme)
add the ability to configure custom transformers.  These are drop-in replacements for the vanilla `typescript` module and
implement the same API.

For example, to use `ttypescript` and `ts-transformer-keys`, add this to your `tsconfig.json`:

```jsonc title="tsconfig.json"
{
  "ts-node": {
    // This can be omitted when using ts-patch
    "compiler": "ttypescript"
  },
  "compilerOptions": {
    // plugin configuration is the same for both ts-patch and ttypescript
    "plugins": [
      { "transform": "ts-transformer-keys/transformer" }
    ]
  }
}
```

## Transpilers

ts-node supports third-party transpilers as plugins.  Transpilers such as swc can transform TypeScript into JavaScript
much faster than the TypeScript compiler.  You will still benefit from ts-node's automatic `tsconfig.json` discovery,
sourcemap support, and global ts-node CLI. Plugins automatically derive an appropriate configuration from your existing
`tsconfig.json` which simplifies project boilerplate.

> **What is the difference between a compiler and a transpiler?**
>
> For our purposes, a compiler implements TypeScript's API and can perform typechecking.
> A third-party transpiler does not.  Both transform TypeScript into JavaScript.

### Third-party plugins

The `transpiler` option allows using third-party transpiler plugins with ts-node.  `transpiler` must be given the
name of a module which can be `require()`d.  The built-in `swc` plugin is exposed as `ts-node/transpilers/swc`.

For example, to use a hypothetical "@cspotcode/fast-ts-compiler", first install it into your project: `npm install @cspotcode/fast-ts-compiler`

Then add the following to your tsconfig:

```jsonc title="tsconfig.json"
{
  "ts-node": {
    "transpileOnly": true,
    "transpiler": "@cspotcode/fast-ts-compiler"
  }
}
```

### Write your own plugin

To write your own transpiler plugin, check our [API docs](https://typestrong.org/ts-node/api/interfaces/TranspilerModule.html).

Plugins are `require()`d by ts-node, so they can be a local script or a node module published to npm.  The module must
export a `create` function described by our
[`TranspilerModule`](https://typestrong.org/ts-node/api/interfaces/TranspilerModule.html) interface.  `create` is
invoked by ts-node at startup to create one or more transpiler instances.  The instances are used to transform
TypeScript into JavaScript.

For a working example, check out out our bundled swc plugin: https://github.com/TypeStrong/ts-node/blob/main/src/transpilers/swc.ts

## Module type overrides

> Wherever possible, it is recommended to use TypeScript's [`NodeNext` or `Node16` mode](https://www.typescriptlang.org/docs/handbook/esm-node.html) instead of the options described
> in this section.  Setting `"module": "NodeNext"` and using the `.cts` file extension should work well for most projects.

When deciding how a file should be compiled and executed -- as either CommonJS or native ECMAScript module -- ts-node matches
`node` and `tsc` behavior.  This means TypeScript files are transformed according to your `tsconfig.json` `"module"`
option and executed according to node's rules for the `package.json` `"type"` field.  Set `"module": "NodeNext"` and everything should work.

In rare cases, you may need to override this behavior for some files.  For example, some tools read a `name-of-tool.config.ts`
and require that file to execute as CommonJS.  If you have `package.json` configured with `"type": "module"` and `tsconfig.json` with
`"module": "esnext"`, the config is native ECMAScript by default and will raise an error.  You will need to force the config and
any supporting scripts to execute as CommonJS.

In these situations, our `moduleTypes` option can override certain files to be
CommonJS or ESM.  Similar overriding is possible by using `.mts`, `.cts`, `.cjs` and `.mjs` file extensions.
`moduleTypes` achieves the same effect for `.ts` and `.js` files, and *also* overrides your `tsconfig.json` `"module"`
config appropriately.

The following example tells ts-node to execute a webpack config as CommonJS:

```jsonc title="tsconfig.json"
{
  "ts-node": {
    "transpileOnly": true,
    "moduleTypes": {
      "webpack.config.ts": "cjs",
      // Globs are also supported with the same behavior as tsconfig "include"
      "webpack-config-scripts/**/*": "cjs"
    }
  },
  "compilerOptions": {
    "module": "es2020",
    "target": "es2020"
  }
}
```

Each key is a glob pattern with the same syntax as tsconfig's `"include"` array.
When multiple patterns match the same file, the last pattern takes precedence.

*   `cjs` overrides matches files to compile and execute as CommonJS.
*   `esm` overrides matches files to compile and execute as native ECMAScript modules.
*   `package` resets either of the above to default behavior, which obeys `package.json` `"type"` and `tsconfig.json` `"module"` options.

### Caveats

Files with an overridden module type are transformed with the same limitations as [`isolatedModules`](https://www.typescriptlang.org/tsconfig#isolatedModules).  This will only affect rare cases such as using `const enum`s with [`preserveConstEnums`](https://www.typescriptlang.org/tsconfig#preserveConstEnums) disabled.

This feature is meant to facilitate scenarios where normal `compilerOptions` and `package.json` configuration is not possible.  For example, a `webpack.config.ts` cannot be given its own `package.json` to override `"type"`.  Wherever possible you should favor using traditional `package.json` and `tsconfig.json` configurations.

## API

ts-node's complete API is documented here: [API Docs](https://typestrong.org/ts-node/api/)

Here are a few highlights of what you can accomplish:

*   [`create()`](https://typestrong.org/ts-node/api/index.html#create) creates ts-node's compiler service without
    registering any hooks.
*   [`createRepl()`](https://typestrong.org/ts-node/api/index.html#createRepl) creates an instance of our REPL service, so
    you can create your own TypeScript-powered REPLs.
*   [`createEsmHooks()`](https://typestrong.org/ts-node/api/index.html#createEsmHooks) creates our ESM loader hooks,
    suitable for composing with other loaders or augmenting with additional features.

# Recipes

## Watching and restarting

ts-node focuses on adding first-class TypeScript support to node.  Watching files and code reloads are out of scope for the project.

If you want to restart the `ts-node` process on file change, existing node.js tools such as [nodemon](https://github.com/remy/nodemon), [onchange](https://github.com/Qard/onchange) and [node-dev](https://github.com/fgnass/node-dev) work.

There's also [`ts-node-dev`](https://github.com/whitecolor/ts-node-dev), a modified version of [`node-dev`](https://github.com/fgnass/node-dev) using `ts-node` for compilation that will restart the process on file change. Note that `ts-node-dev` is incompatible with our native ESM loader.

## AVA

Assuming you are configuring AVA via your `package.json`, add one of the following configurations.

### CommonJS

Use this configuration if your `package.json` does not have `"type": "module"`.

```jsonc title="package.json"
{
  "ava": {
    "extensions": [
      "ts"
    ],
    "require": [
      "ts-node/register"
    ]
  }
}
```

### Native ECMAScript modules

This configuration is necessary if your `package.json` has `"type": "module"`.

```jsonc title="package.json"
{
  "ava": {
    "extensions": {
      "ts": "module"
    },
    "nonSemVerExperiments": {
      "configurableModuleFormat": true
    },
    "nodeArguments": [
      "--loader=ts-node/esm"
    ]
  }
}
```

## Gulp

ts-node support is built-in to gulp.

```sh
# Create a `gulpfile.ts` and run `gulp`.
gulp
```

See also: https://gulpjs.com/docs/en/getting-started/javascript-and-gulpfiles#transpilation

## IntelliJ and Webstorm

Create a new Node.js configuration and add `-r ts-node/register` to "Node parameters."

**Note:** If you are using the `--project <tsconfig.json>` command line argument as per the [Configuration Options](#configuration), and want to apply this same behavior when launching in IntelliJ, specify under "Environment Variables": `TS_NODE_PROJECT=<tsconfig.json>`.

## Mocha

### Mocha 7 and newer

```shell
mocha --require ts-node/register --extensions ts,tsx --watch --watch-files src 'tests/**/*.{ts,tsx}' [...args]
```

Or specify options via your mocha config file.

```jsonc title=".mocharc.json"
{
  // Specify "require" for CommonJS
  "require": "ts-node/register",
  // Specify "loader" for native ESM
  "loader": "ts-node/esm",
  "extensions": ["ts", "tsx"],
  "spec": [
    "tests/**/*.spec.*"
  ],
  "watch-files": [
    "src"
  ]
}
```

See also: https://mochajs.org/#configuring-mocha-nodejs

### Mocha <=6

```shell
mocha --require ts-node/register --watch-extensions ts,tsx "test/**/*.{ts,tsx}" [...args]
```

**Note:** `--watch-extensions` is only used in `--watch` mode.

## Tape

```shell
ts-node node_modules/tape/bin/tape [...args]
```

## Visual Studio Code

Create a new Node.js debug configuration, add `-r ts-node/register` to node args and move the `program` to the `args` list (so VS Code doesn't look for `outFiles`).

```jsonc title=".vscode/launch.json"
{
    "configurations": [{
        "type": "node",
        "request": "launch",
        "name": "Launch Program",
        "runtimeArgs": [
            "-r",
            "ts-node/register"
        ],
        "args": [
            "${workspaceFolder}/src/index.ts"
        ]
    }],
}
```

**Note:** If you are using the `--project <tsconfig.json>` command line argument as per the [Configuration Options](#configuration), and want to apply this same behavior when launching in VS Code, add an "env" key into the launch configuration: `"env": { "TS_NODE_PROJECT": "<tsconfig.json>" }`.

## Other

In many cases, setting [`NODE_OPTIONS`](https://nodejs.org/api/cli.html#cli_node_options_options) will enable `ts-node` within other node tools, child processes, and worker threads.

```shell
NODE_OPTIONS="-r ts-node/register"
```

Or, if you require native ESM support:

```shell
NODE_OPTIONS="--loader ts-node/esm"
```

This tells any node processes which receive this environment variable to install `ts-node`'s hooks before executing other code.

# License

ts-node is licensed under the MIT license.  [MIT](https://github.com/TypeStrong/ts-node/blob/main/LICENSE)

ts-node includes source code from Node.js which is licensed under the MIT license.  [Node.js license information](https://raw.githubusercontent.com/nodejs/node/master/LICENSE)

ts-node includes source code from the TypeScript compiler which is licensed under the Apache License 2.0.  [TypeScript license information](https://github.com/microsoft/TypeScript/blob/master/LICENSE.txt)
