{"version": 3, "file": "argv-payload.js", "sourceRoot": "", "sources": ["../../src/child/argv-payload.ts"], "names": [], "mappings": ";;;AAAA,+BAA2E;AAE3E,gBAAgB;AACH,QAAA,SAAS,GAAG,yBAAyB,CAAC;AAEnD,gBAAgB;AAChB,SAAgB,QAAQ,CAAC,MAAW;IAClC,OAAO,IAAA,yBAAkB,EAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,EAAE;QACrE,CAAC,gBAAS,CAAC,oBAAoB,CAAC,EAAE,gBAAS,CAAC,kBAAkB;KAC/D,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AACxB,CAAC;AAJD,4BAIC;AAED,gBAAgB;AAChB,SAAgB,UAAU,CAAC,GAAW;IACpC,OAAO,IAAI,CAAC,KAAK,CACf,IAAA,2BAAoB,EAAC,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC,QAAQ,EAAE,CAC5D,CAAC;AACJ,CAAC;AAJD,gCAIC", "sourcesContent": ["import { brotliCompressSync, brotliDecompressSync, constants } from 'zlib';\n\n/** @internal */\nexport const argPrefix = '--brotli-base64-config=';\n\n/** @internal */\nexport function compress(object: any) {\n  return brotliCompressSync(Buffer.from(JSON.stringify(object), 'utf8'), {\n    [constants.BROTLI_PARAM_QUALITY]: constants.BROTLI_MIN_QUALITY,\n  }).toString('base64');\n}\n\n/** @internal */\nexport function decompress(str: string) {\n  return JSON.parse(\n    brotliDecompressSync(Buffer.from(str, 'base64')).toString()\n  );\n}\n"]}