import { Resource, CreateResourceRequest, UpdateResourceRequest, PatchResourceRequest, ResourceFilters, ApiResponse, PaginatedResponse } from '../types';
declare class ResourceService {
    getAllResources(filters?: ResourceFilters): Promise<PaginatedResponse<Resource[]>>;
    getResourceById(id: string | number): Promise<ApiResponse<Resource>>;
    createResource(data: CreateResourceRequest): Promise<ApiResponse<Resource>>;
    updateResource(id: string | number, data: UpdateResourceRequest): Promise<ApiResponse<Resource>>;
    patchResource(id: string | number, data: PatchResourceRequest): Promise<ApiResponse<Resource>>;
    deleteResource(id: string | number): Promise<ApiResponse>;
    clearAllResources(): Promise<ApiResponse>;
}
declare const _default: ResourceService;
export default _default;
