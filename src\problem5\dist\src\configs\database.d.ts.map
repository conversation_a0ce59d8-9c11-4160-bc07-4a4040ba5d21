{"version": 3, "file": "database.d.ts", "sourceRoot": "", "sources": ["../../../src/configs/database.ts"], "names": [], "mappings": "AAAA,OAAO,OAAO,MAAM,SAAS,CAAC;AAG9B,OAAO,EAAE,cAAc,EAAE,WAAW,EAAE,MAAM,UAAU,CAAC;AAEvD,cAAM,QAAQ;IACV,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAyB;IAChD,OAAO,CAAC,MAAM,CAAC,cAAc,CAAkB;IAC/C,OAAO,CAAC,EAAE,CAAiC;IAC3C,OAAO,CAAC,MAAM,CAAS;IACvB,OAAO,CAAC,WAAW,CAAkB;IACrC,OAAO,CAAC,WAAW,CAA8B;IAEjD,OAAO;WAIO,WAAW,IAAI,QAAQ;YAWvB,IAAI;IAoClB,OAAO,CAAC,YAAY;YA2BN,iBAAiB;IAO/B,KAAK,IAAI,OAAO,CAAC,QAAQ,GAAG,IAAI;IAK1B,GAAG,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,GAAE,GAAG,EAAO,GAAG,OAAO,CAAC,cAAc,CAAC;IAc7D,GAAG,CAAC,CAAC,GAAG,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,GAAE,GAAG,EAAO,GAAG,OAAO,CAAC,CAAC,GAAG,SAAS,CAAC;IAcrE,GAAG,CAAC,CAAC,GAAG,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,GAAE,GAAG,EAAO,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC;IAcjE,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;IAchB,YAAY,IAAI,OAAO,CAAC,WAAW,CAAC;CAa7C;;AAGD,wBAAsC"}