"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const chai_1 = __importDefault(require("chai"));
const chai_http_1 = __importDefault(require("chai-http"));
const chai_2 = require("chai");
const server_1 = __importDefault(require("../src/server"));
const resourceService_1 = __importDefault(require("../src/services/resourceService"));
chai_1.default.use(chai_http_1.default);
describe('Simple CRUD API Tests', () => {
    beforeEach(async () => {
        // Clear all resources before each test
        await resourceService_1.default.clearAllResources();
    });
    it('Should return health status', (done) => {
        chai_1.default.request(server_1.default)
            .get('/health')
            .end((err, res) => {
            (0, chai_2.expect)(res).to.have.status(200);
            (0, chai_2.expect)(res.body.success).to.be.true;
            (0, chai_2.expect)(res.body.message).to.equal('Server is running');
            done();
        });
    });
    it('Should get empty resources list', (done) => {
        chai_1.default.request(server_1.default)
            .get('/api/resources')
            .end((err, res) => {
            (0, chai_2.expect)(res).to.have.status(200);
            (0, chai_2.expect)(res.body.success).to.be.true;
            (0, chai_2.expect)(res.body.data).to.be.an('array');
            (0, chai_2.expect)(res.body.data).to.have.length(0);
            done();
        });
    });
    it('Should create a resource', (done) => {
        const newResource = {
            name: 'Test Resource',
            description: 'A test resource',
            category: 'Testing'
        };
        chai_1.default.request(server_1.default)
            .post('/api/resources')
            .send(newResource)
            .end((err, res) => {
            (0, chai_2.expect)(res).to.have.status(201);
            (0, chai_2.expect)(res.body.success).to.be.true;
            (0, chai_2.expect)(res.body.data.name).to.equal(newResource.name);
            (0, chai_2.expect)(res.body.data.description).to.equal(newResource.description);
            (0, chai_2.expect)(res.body.data.category).to.equal(newResource.category);
            (0, chai_2.expect)(res.body.data.id).to.be.a('number');
            done();
        });
    });
    it('Should get a resource by ID', (done) => {
        const newResource = {
            name: 'Findable Resource',
            description: 'This can be found',
            category: 'Testing'
        };
        chai_1.default.request(server_1.default)
            .post('/api/resources')
            .send(newResource)
            .end((err, res) => {
            const resourceId = res.body.data.id;
            chai_1.default.request(server_1.default)
                .get(`/api/resources/${resourceId}`)
                .end((err, res) => {
                (0, chai_2.expect)(res).to.have.status(200);
                (0, chai_2.expect)(res.body.success).to.be.true;
                (0, chai_2.expect)(res.body.data.id).to.equal(resourceId);
                (0, chai_2.expect)(res.body.data.name).to.equal(newResource.name);
                done();
            });
        });
    });
    it('Should update a resource with PUT (full replacement)', (done) => {
        const originalResource = {
            name: 'Original Name',
            description: 'Original description',
            category: 'Original'
        };
        const updateData = {
            name: 'Updated Name',
            description: 'Updated description',
            category: 'Updated Category',
            status: 'inactive'
        };
        chai_1.default.request(server_1.default)
            .post('/api/resources')
            .send(originalResource)
            .end((err, res) => {
            const resourceId = res.body.data.id;
            chai_1.default.request(server_1.default)
                .put(`/api/resources/${resourceId}`)
                .send(updateData)
                .end((err, res) => {
                (0, chai_2.expect)(res).to.have.status(200);
                (0, chai_2.expect)(res.body.success).to.be.true;
                (0, chai_2.expect)(res.body.data.name).to.equal(updateData.name);
                (0, chai_2.expect)(res.body.data.description).to.equal(updateData.description);
                (0, chai_2.expect)(res.body.data.category).to.equal(updateData.category);
                (0, chai_2.expect)(res.body.data.status).to.equal(updateData.status);
                done();
            });
        });
    });
    it('Should require name and description for PUT', (done) => {
        const originalResource = {
            name: 'Original Name',
            description: 'Original description',
            category: 'Original'
        };
        chai_1.default.request(server_1.default)
            .post('/api/resources')
            .send(originalResource)
            .end((err, res) => {
            const resourceId = res.body.data.id;
            chai_1.default.request(server_1.default)
                .put(`/api/resources/${resourceId}`)
                .send({ name: 'Only Name' }) // Missing description
                .end((err, res) => {
                (0, chai_2.expect)(res).to.have.status(400);
                (0, chai_2.expect)(res.body.success).to.be.false;
                (0, chai_2.expect)(res.body.error).to.equal('Description is required for PUT operation');
                done();
            });
        });
    });
    it('Should patch a resource (partial update)', (done) => {
        const originalResource = {
            name: 'Original Name',
            description: 'Original description',
            category: 'Original'
        };
        const patchData = {
            name: 'Patched Name'
            // Only updating name, other fields should remain unchanged
        };
        chai_1.default.request(server_1.default)
            .post('/api/resources')
            .send(originalResource)
            .end((err, res) => {
            const resourceId = res.body.data.id;
            chai_1.default.request(server_1.default)
                .patch(`/api/resources/${resourceId}`)
                .send(patchData)
                .end((err, res) => {
                (0, chai_2.expect)(res).to.have.status(200);
                (0, chai_2.expect)(res.body.success).to.be.true;
                (0, chai_2.expect)(res.body.data.name).to.equal(patchData.name);
                (0, chai_2.expect)(res.body.data.description).to.equal(originalResource.description); // Unchanged
                (0, chai_2.expect)(res.body.data.category).to.equal(originalResource.category); // Unchanged
                (0, chai_2.expect)(res.body.data.status).to.equal('active'); // Default unchanged
                done();
            });
        });
    });
    it('Should validate status in PATCH', (done) => {
        const originalResource = {
            name: 'Original Name',
            description: 'Original description'
        };
        chai_1.default.request(server_1.default)
            .post('/api/resources')
            .send(originalResource)
            .end((err, res) => {
            const resourceId = res.body.data.id;
            chai_1.default.request(server_1.default)
                .patch(`/api/resources/${resourceId}`)
                .send({ status: 'invalid_status' })
                .end((err, res) => {
                (0, chai_2.expect)(res).to.have.status(400);
                (0, chai_2.expect)(res.body.success).to.be.false;
                (0, chai_2.expect)(res.body.error).to.equal('Status must be either "active" or "inactive"');
                done();
            });
        });
    });
    it('Should delete a resource', (done) => {
        const newResource = {
            name: 'To Delete',
            description: 'This will be deleted',
            category: 'Testing'
        };
        chai_1.default.request(server_1.default)
            .post('/api/resources')
            .send(newResource)
            .end((err, res) => {
            const resourceId = res.body.data.id;
            chai_1.default.request(server_1.default)
                .delete(`/api/resources/${resourceId}`)
                .end((err, res) => {
                (0, chai_2.expect)(res).to.have.status(200);
                (0, chai_2.expect)(res.body.success).to.be.true;
                (0, chai_2.expect)(res.body.message).to.equal('Resource deleted successfully');
                // Verify it's deleted
                chai_1.default.request(server_1.default)
                    .get(`/api/resources/${resourceId}`)
                    .end((err, res) => {
                    (0, chai_2.expect)(res).to.have.status(404);
                    done();
                });
            });
        });
    });
    it('Should return 404 for non-existent resource', (done) => {
        chai_1.default.request(server_1.default)
            .get('/api/resources/999')
            .end((err, res) => {
            (0, chai_2.expect)(res).to.have.status(404);
            (0, chai_2.expect)(res.body.success).to.be.false;
            (0, chai_2.expect)(res.body.error).to.equal('Resource not found');
            done();
        });
    });
    it('Should return 400 for missing required fields', (done) => {
        chai_1.default.request(server_1.default)
            .post('/api/resources')
            .send({ description: 'No name provided' })
            .end((err, res) => {
            (0, chai_2.expect)(res).to.have.status(400);
            (0, chai_2.expect)(res.body.success).to.be.false;
            (0, chai_2.expect)(res.body.error).to.equal('Name is required');
            done();
        });
    });
    it('Should filter resources by category', (done) => {
        const resource1 = { name: 'Resource 1', description: 'First', category: 'CategoryA' };
        const resource2 = { name: 'Resource 2', description: 'Second', category: 'CategoryB' };
        // Create two resources with different categories
        chai_1.default.request(server_1.default)
            .post('/api/resources')
            .send(resource1)
            .end(() => {
            chai_1.default.request(server_1.default)
                .post('/api/resources')
                .send(resource2)
                .end(() => {
                // Filter by CategoryA
                chai_1.default.request(server_1.default)
                    .get('/api/resources?category=CategoryA')
                    .end((err, res) => {
                    (0, chai_2.expect)(res).to.have.status(200);
                    (0, chai_2.expect)(res.body.data).to.have.length(1);
                    (0, chai_2.expect)(res.body.data[0].category).to.equal('CategoryA');
                    done();
                });
            });
        });
    });
});
//# sourceMappingURL=api.test.js.map