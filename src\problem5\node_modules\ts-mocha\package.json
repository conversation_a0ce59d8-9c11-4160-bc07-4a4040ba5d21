{"name": "ts-mocha", "version": "10.1.0", "description": "Mocha thin wrapper that allows running TypeScript tests with TypeScript runtime (ts-node) to get rid of compilation complexity", "repository": "https://github.com/piotrwitek/ts-mocha", "author": "<PERSON><PERSON><PERSON> <<EMAIL>> (http://piotrwitek.github.io)", "license": "MIT", "main": "src/index.js", "bin": {"ts-mocha": "./bin/ts-mocha"}, "engines": {"node": ">= 6.X.X"}, "scripts": {"clean": "rm -rf node_modules/", "reinstall": "npm run clean && npm install", "pretest:baseline": "npm install --peer", "test:baseline": "./bin/ts-mocha -p test/baseline/tsconfig.json test/baseline/**/*.spec.ts test/typecheck/**/*.spec.ts && node test/baseline/programmatic-use-test.js", "pretest:paths": "npm install --optional", "test:paths": "./bin/ts-mocha --paths -p test/paths/tsconfig.json test/paths/**/*.spec.ts && node test/paths/programmatic-use-test.js", "test:typecheck": "if ./bin/ts-mocha --type-check -p test/typecheck/tsconfig.json test/typecheck/**/*.spec.ts; then exit 1; fi && node test/typecheck/programmatic-use-test.js", "test": "npm run test:baseline & npm run test:paths & npm run test:typecheck", "prepublishOnly": "npm run clean && npm install --production && npm install -D mocha && npm test"}, "dependencies": {"ts-node": "7.0.1"}, "devDependencies": {"@types/expect": "24.3.0", "@types/mocha": "8.0.3", "@types/node": "14.14.2", "expect": "26.6.1", "husky": "4.3.0", "mocha": "^11.0.1", "tslint": "6.1.3", "typescript": "3.3.3"}, "peerDependencies": {"mocha": "^3.X.X || ^4.X.X || ^5.X.X || ^6.X.X || ^7.X.X || ^8.X.X || ^9.X.X || ^10.X.X || ^11.X.X"}, "optionalDependencies": {"tsconfig-paths": "^3.5.0"}, "files": ["bin/", "src/", "package.json", "package-lock.json", "README.md", "CHANGELOG.md", "LICENSE"], "keywords": ["ts", "mocha", "typescript", "ts-node", "test"], "greenkeeper": {"ignore": ["@types/expect", "@types/mocha", "@types/node", "expect", "husky", "tslint"]}}