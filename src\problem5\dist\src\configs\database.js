"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const sqlite3_1 = __importDefault(require("sqlite3"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
class Database {
    constructor() {
        this.db = null;
        this.initialized = false;
        this.initPromise = null;
        this.dbPath = path_1.default.join(__dirname, '../../data/resources.db');
    }
    static getInstance() {
        if (!Database.instance) {
            Database.instance = new Database();
            // Only initialize if not already initialized
            if (!Database.instance.initPromise) {
                Database.instance.initPromise = Database.instance.init();
            }
        }
        return Database.instance;
    }
    async init() {
        if (this.initialized || Database.isInitializing) {
            return;
        }
        Database.isInitializing = true;
        return new Promise((resolve, reject) => {
            // Ensure data directory exists
            const dataDir = path_1.default.dirname(this.dbPath);
            if (!fs_1.default.existsSync(dataDir)) {
                fs_1.default.mkdirSync(dataDir, { recursive: true });
                console.log('📁 Created data directory');
            }
            // Connect to database
            this.db = new sqlite3_1.default.Database(this.dbPath, (err) => {
                if (err) {
                    console.error('❌ Database connection error:', err.message);
                    Database.isInitializing = false;
                    reject(err);
                }
                else {
                    console.log('🗄️ Connected to SQLite database');
                    this.createTables().then(() => {
                        this.initialized = true;
                        Database.isInitializing = false;
                        resolve();
                    }).catch((error) => {
                        Database.isInitializing = false;
                        reject(error);
                    });
                }
            });
        });
    }
    createTables() {
        return new Promise((resolve, reject) => {
            const createResourcesTable = `
                CREATE TABLE IF NOT EXISTS resources (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    description TEXT NOT NULL,
                    category TEXT NOT NULL DEFAULT 'General',
                    status TEXT NOT NULL DEFAULT 'active' CHECK(status IN ('active', 'inactive')),
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            `;
            this.db.run(createResourcesTable, (err) => {
                if (err) {
                    console.error('❌ Error creating resources table:', err.message);
                    reject(err);
                }
                else {
                    console.log('✅ Resources table ready');
                    resolve();
                }
            });
        });
    }
    // Ensure database is initialized before operations
    async ensureInitialized() {
        if (!this.initialized && this.initPromise) {
            await this.initPromise;
        }
    }
    // Get database instance
    getDb() {
        return this.db;
    }
    // Run a query
    async run(sql, params = []) {
        await this.ensureInitialized();
        return new Promise((resolve, reject) => {
            this.db.run(sql, params, function (err) {
                if (err) {
                    reject(err);
                }
                else {
                    resolve({ id: this.lastID, changes: this.changes });
                }
            });
        });
    }
    // Get single row
    async get(sql, params = []) {
        await this.ensureInitialized();
        return new Promise((resolve, reject) => {
            this.db.get(sql, params, (err, row) => {
                if (err) {
                    reject(err);
                }
                else {
                    resolve(row);
                }
            });
        });
    }
    // Get all rows
    async all(sql, params = []) {
        await this.ensureInitialized();
        return new Promise((resolve, reject) => {
            this.db.all(sql, params, (err, rows) => {
                if (err) {
                    reject(err);
                }
                else {
                    resolve(rows);
                }
            });
        });
    }
    // Close database connection
    close() {
        return new Promise((resolve, reject) => {
            this.db.close((err) => {
                if (err) {
                    reject(err);
                }
                else {
                    console.log('🔒 Database connection closed');
                    resolve();
                }
            });
        });
    }
    // Clear all data (useful for testing)
    async clearAllData() {
        try {
            await this.ensureInitialized();
            await this.run('DELETE FROM resources');
            await this.run('DELETE FROM sqlite_sequence WHERE name="resources"');
            console.log('🧹 All data cleared');
            return { success: true, message: 'All data cleared' };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            console.error('❌ Error clearing data:', errorMessage);
            return { success: false, error: errorMessage };
        }
    }
}
Database.instance = null;
Database.isInitializing = false;
// Export singleton instance
exports.default = Database.getInstance();
