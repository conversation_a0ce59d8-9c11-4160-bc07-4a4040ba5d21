import sqlite3 from 'sqlite3';
import { DatabaseResult, ApiResponse } from '../types';
declare class Database {
    private db;
    private dbPath;
    constructor();
    private init;
    private createTables;
    getDb(): sqlite3.Database | null;
    run(sql: string, params?: any[]): Promise<DatabaseResult>;
    get<T = any>(sql: string, params?: any[]): Promise<T | undefined>;
    all<T = any>(sql: string, params?: any[]): Promise<T[]>;
    close(): Promise<void>;
    clearAllData(): Promise<ApiResponse>;
}
declare const database: Database;
export default database;
//# sourceMappingURL=database.d.ts.map