import sqlite3 from 'sqlite3';
import { DatabaseResult, ApiResponse } from '../types';
declare class Database {
    private static instance;
    private static isInitializing;
    private db;
    private dbPath;
    private initialized;
    private initPromise;
    private constructor();
    static getInstance(): Database;
    private init;
    private createTables;
    private ensureInitialized;
    getDb(): sqlite3.Database | null;
    run(sql: string, params?: any[]): Promise<DatabaseResult>;
    get<T = any>(sql: string, params?: any[]): Promise<T | undefined>;
    all<T = any>(sql: string, params?: any[]): Promise<T[]>;
    close(): Promise<void>;
    clearAllData(): Promise<ApiResponse>;
}
declare const _default: Database;
export default _default;
