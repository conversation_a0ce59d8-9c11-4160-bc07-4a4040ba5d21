"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const database_1 = __importDefault(require("../configs/database"));
class ResourceService {
    // Get all resources with optional filtering
    async getAllResources(filters = {}) {
        try {
            let sql = 'SELECT * FROM resources WHERE 1=1';
            const params = [];
            // Filter by category
            if (filters.category) {
                sql += ' AND category LIKE ?';
                params.push(`%${filters.category}%`);
            }
            // Filter by status
            if (filters.status) {
                sql += ' AND status = ?';
                params.push(filters.status);
            }
            // Search in name and description
            if (filters.search) {
                sql += ' AND (name LIKE ? OR description LIKE ?)';
                const searchTerm = `%${filters.search}%`;
                params.push(searchTerm, searchTerm);
            }
            sql += ' ORDER BY created_at DESC';
            const resources = await database_1.default.all(sql, params);
            return {
                success: true,
                data: resources,
                pagination: {
                    total: resources.length,
                    count: resources.length
                }
            };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            return {
                success: false,
                error: errorMessage
            };
        }
    }
    // Get resource by ID
    async getResourceById(id) {
        try {
            const resource = await database_1.default.get('SELECT * FROM resources WHERE id = ?', [id]);
            if (!resource) {
                return {
                    success: false,
                    error: 'Resource not found'
                };
            }
            return {
                success: true,
                data: resource
            };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            return {
                success: false,
                error: errorMessage
            };
        }
    }
    // Create new resource
    async createResource(data) {
        try {
            // Validation
            if (!data.name || data.name.trim() === '') {
                return {
                    success: false,
                    error: 'Name is required'
                };
            }
            if (!data.description) {
                return {
                    success: false,
                    error: 'Description is required'
                };
            }
            // Insert into database
            const sql = `
                INSERT INTO resources (name, description, category, status)
                VALUES (?, ?, ?, ?)
            `;
            const params = [
                data.name.trim(),
                data.description.trim(),
                data.category || 'General',
                data.status || 'active'
            ];
            const result = await database_1.default.run(sql, params);
            // Get the created resource
            const resource = await database_1.default.get('SELECT * FROM resources WHERE id = ?', [result.id]);
            return {
                success: true,
                data: resource,
                message: 'Resource created successfully'
            };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            return {
                success: false,
                error: errorMessage
            };
        }
    }
    // Update resource (PUT - replace entire resource)
    async updateResource(id, data) {
        try {
            // Check if resource exists
            const existing = await database_1.default.get('SELECT * FROM resources WHERE id = ?', [id]);
            if (!existing) {
                return {
                    success: false,
                    error: 'Resource not found'
                };
            }
            // PUT requires name and description (core fields)
            if (!data.name || data.name.trim() === '') {
                return {
                    success: false,
                    error: 'Name is required for PUT operation'
                };
            }
            if (!data.description || data.description.trim() === '') {
                return {
                    success: false,
                    error: 'Description is required for PUT operation'
                };
            }
            // PUT replaces the entire resource with provided data
            const sql = `
                UPDATE resources
                SET name = ?, description = ?, category = ?, status = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            `;
            const params = [
                data.name.trim(),
                data.description.trim(),
                data.category || 'General',
                data.status || 'active',
                id
            ];
            await database_1.default.run(sql, params);
            // Get updated resource
            const resource = await database_1.default.get('SELECT * FROM resources WHERE id = ?', [id]);
            return {
                success: true,
                data: resource,
                message: 'Resource updated successfully'
            };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            return {
                success: false,
                error: errorMessage
            };
        }
    }
    // Patch resource (PATCH - partial update)
    async patchResource(id, data) {
        try {
            // Check if resource exists
            const existing = await database_1.default.get('SELECT * FROM resources WHERE id = ?', [id]);
            if (!existing) {
                return {
                    success: false,
                    error: 'Resource not found'
                };
            }
            // Build update query for only provided fields
            const updates = [];
            const params = [];
            if (data.name !== undefined) {
                if (data.name.trim() === '') {
                    return {
                        success: false,
                        error: 'Name cannot be empty'
                    };
                }
                updates.push('name = ?');
                params.push(data.name.trim());
            }
            if (data.description !== undefined) {
                if (data.description.trim() === '') {
                    return {
                        success: false,
                        error: 'Description cannot be empty'
                    };
                }
                updates.push('description = ?');
                params.push(data.description.trim());
            }
            if (data.category !== undefined) {
                updates.push('category = ?');
                params.push(data.category || 'General');
            }
            if (data.status !== undefined) {
                // Validate status value
                if (!['active', 'inactive'].includes(data.status)) {
                    return {
                        success: false,
                        error: 'Status must be either "active" or "inactive"'
                    };
                }
                updates.push('status = ?');
                params.push(data.status);
            }
            if (updates.length === 0) {
                return {
                    success: true,
                    data: existing,
                    message: 'No changes made'
                };
            }
            updates.push('updated_at = CURRENT_TIMESTAMP');
            params.push(id);
            const sql = `UPDATE resources SET ${updates.join(', ')} WHERE id = ?`;
            await database_1.default.run(sql, params);
            // Get updated resource
            const resource = await database_1.default.get('SELECT * FROM resources WHERE id = ?', [id]);
            return {
                success: true,
                data: resource,
                message: 'Resource patched successfully'
            };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            return {
                success: false,
                error: errorMessage
            };
        }
    }
    // Delete resource
    async deleteResource(id) {
        try {
            // Check if resource exists
            const existing = await database_1.default.get('SELECT * FROM resources WHERE id = ?', [id]);
            if (!existing) {
                return {
                    success: false,
                    error: 'Resource not found'
                };
            }
            await database_1.default.run('DELETE FROM resources WHERE id = ?', [id]);
            return {
                success: true,
                message: 'Resource deleted successfully'
            };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            return {
                success: false,
                error: errorMessage
            };
        }
    }
    // Clear all resources (useful for testing)
    async clearAllResources() {
        try {
            return await database_1.default.clearAllData();
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            return {
                success: false,
                error: errorMessage
            };
        }
    }
}
exports.default = new ResourceService();
