import { Request, Response } from 'express';
declare class ResourceController {
    getAllResources(req: Request, res: Response): Promise<void>;
    getResourceById(req: Request, res: Response): Promise<void>;
    createResource(req: Request, res: Response): Promise<void>;
    updateResource(req: Request, res: Response): Promise<void>;
    patchResource(req: Request, res: Response): Promise<void>;
    deleteResource(req: Request, res: Response): Promise<void>;
}
declare const _default: ResourceController;
export default _default;
