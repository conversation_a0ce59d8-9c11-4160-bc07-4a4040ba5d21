"use strict";var S=Object.defineProperty;var l=(e,t)=>S(e,"name",{value:t,configurable:!0});var _=require("node:worker_threads"),m=require("../node-features-roYmp9jK.cjs"),F=require("../register-2sWVXuRQ.cjs");require("../get-pipe-path-BoR10qr8.cjs"),require("node:module");var p=require("node:path"),g=require("node:url");require("get-tsconfig");var s=require("../register-D46fvsV_.cjs"),E=require("node:fs");require("esbuild"),require("node:crypto");var f=require("../index-gckBtVBf.cjs"),v=require("../client-D6NvIMSC.cjs");require("../require-D4F1Lv60.cjs");var L=require("node:fs/promises");require("module"),require("../temporary-directory-B83uKxJF.cjs"),require("node:os"),require("node:util"),require("../index-BWFBUo6r.cjs"),require("node:net");const u={active:!0},b=l(async e=>{if(!e)throw new Error(`tsx must be loaded with --import instead of --loader
The --loader flag was deprecated in Node v20.6.0 and v18.19.0`);u.namespace=e.namespace,e.tsconfig!==!1&&s.loadTsconfig(e.tsconfig??process.env.TSX_TSCONFIG_PATH),e.port&&(u.port=e.port,e.port.on("message",t=>{t==="deactivate"&&(u.active=!1,e.port.postMessage({type:"deactivated"}))}))},"initialize"),J=l(()=>(s.loadTsconfig(process.env.TSX_TSCONFIG_PATH),"process.setSourceMapsEnabled(true);"),"globalPreload"),h=new Map,M=l(async e=>{if(h.has(e))return h.get(e);if(!await E.promises.access(e).then(()=>!0,()=>!1)){h.set(e,void 0);return}const n=await E.promises.readFile(e,"utf8");try{const r=JSON.parse(n);return h.set(e,r),r}catch{throw new Error(`Error parsing: ${e}`)}},"readPackageJson"),N=l(async e=>{let t=new URL("package.json",e);for(;!t.pathname.endsWith("/node_modules/package.json");){const n=g.fileURLToPath(t),r=await M(n);if(r)return r;const a=t;if(t=new URL("../package.json",t),t.pathname===a.pathname)break}},"findPackageJson"),O=l(async e=>(await N(e))?.type??"commonjs","getPackageType"),A=l(e=>{[e]=e.split("?");const t=p.extname(e);if(t===".mts")return"module";if(t===".cts")return"commonjs"},"getFormatFromExtension"),W=l(e=>{const t=A(e);if(t)return t;const{pathname:n}=new URL(e),r=p.extname(n);if(s.tsExtensions.includes(r))return O(e)},"getFormatFromFileUrl"),P="tsx-namespace=",y=l(e=>{const t=e.indexOf(P);if(t===-1)return;const n=e[t-1];if(n!=="?"&&n!=="&")return;const r=t+P.length,a=e.indexOf("&",r);return a===-1?e.slice(r):e.slice(r,a)},"getNamespace"),R=m.isFeatureSupported(m.importAttributes)?"importAttributes":"importAssertions";if(exports.load=async(e,t,n)=>{if(!u.active)return n(e,t);const r=y(e);if(u.namespace&&u.namespace!==r)return n(e,t);if(u.port){const o=new URL(e);o.searchParams.delete("tsx-namespace"),u.port.postMessage({type:"load",url:o.toString()})}if(v.parent.send&&v.parent.send({type:"dependency",path:e}),s.isJsonPattern.test(e)){let o=t[R];o||(o={},t[R]=o),o.type||(o.type="json")}const a=await n(e,t);s.logEsm(3,"loaded by next loader",{url:e,loaded:a});const i=e.startsWith(s.fileUrlPrefix)?g.fileURLToPath(e):e;if(a.format==="commonjs"&&m.isFeatureSupported(m.esmLoadReadFile)&&a.responseURL?.startsWith("file:")&&!i.endsWith(".cjs")){const o=await L.readFile(new URL(e),"utf8");if(!i.endsWith(".js")||f.isESM(o)){const d=f.transformSync(o,i,{tsconfigRaw:s.fileMatcher?.(i)}),T=r?`${i}?namespace=${encodeURIComponent(r)}`:i;return a.responseURL=`data:text/javascript,${encodeURIComponent(d.code)}?filePath=${encodeURIComponent(T)}`,s.logEsm(3,"returning CJS export annotation",a),a}}if(!a.source)return a;const c=a.source.toString();if(a.format==="json"||s.tsExtensionsPattern.test(e)){const o=await f.transform(c,i,{tsconfigRaw:p.isAbsolute(i)?s.fileMatcher?.(i):void 0});return{format:"module",source:s.inlineSourceMap(o)}}if(a.format==="module"){const o=f.transformDynamicImport(i,c);o&&(a.source=s.inlineSourceMap(o))}return a},s.debugEnabled){const e=exports.load;exports.load=async(t,n,r)=>{s.logEsm(2,"load",{url:t,context:n});const a=await e(t,n,r);return s.logEsm(1,"loaded",{url:t,result:a}),a}}const U=l(e=>{if(e.url)return e.url;const t=e.message.match(/^Cannot find module '([^']+)'/);if(t){const[,r]=t;return r}const n=e.message.match(/^Cannot find package '([^']+)'/);if(n){const[,r]=n;if(!p.isAbsolute(r))return;const a=g.pathToFileURL(r);if(a.pathname.endsWith("/")&&(a.pathname+="package.json"),a.pathname.endsWith("/package.json")){const i=f.readJsonFile(a);if(i?.main)return new URL(i.main,a).toString()}else return a.toString()}},"getMissingPathFromNotFound"),w=l(async(e,t,n,r)=>{const a=s.mapTsExtensions(e);if(s.logEsm(3,"resolveExtensions",{url:e,context:t,throwError:r,tryPaths:a}),!a)return;let i;for(const c of a)try{return await n(c,t)}catch(o){const{code:d}=o;if(d!=="ERR_MODULE_NOT_FOUND"&&d!=="ERR_PACKAGE_PATH_NOT_EXPORTED")throw o;i=o}if(r)throw i},"resolveExtensions"),j=l(async(e,t,n)=>{if(s.logEsm(3,"resolveBase",{specifier:e,context:t,specifierStartsWithFileUrl:e.startsWith(s.fileUrlPrefix),isRelativePath:s.isRelativePath(e),tsExtensionsPattern:s.tsExtensionsPattern.test(t.parentURL),allowJs:s.allowJs}),(e.startsWith(s.fileUrlPrefix)||s.isRelativePath(e))&&(s.tsExtensionsPattern.test(t.parentURL)||s.allowJs)){const r=await w(e,t,n);if(s.logEsm(3,"resolveBase resolved",{specifier:e,context:t,resolved:r}),r)return r}try{return await n(e,t)}catch(r){if(s.logEsm(3,"resolveBase error",{specifier:e,context:t,error:r}),r instanceof Error){const a=r;if(a.code==="ERR_MODULE_NOT_FOUND"){const i=U(a);if(i){const c=await w(i,t,n);if(c)return c}}}throw r}},"resolveBase"),q=l(async(e,t,n)=>{if(s.logEsm(3,"resolveDirectory",{specifier:e,context:t,isDirectory:s.isDirectoryPattern.test(e)}),(e==="."||e===".."||e.endsWith("/.."))&&(e+="/"),s.isDirectoryPattern.test(e)){const r=new URL(e,t.parentURL);return r.pathname=p.join(r.pathname,"index"),await w(r.toString(),t,n,!0)}try{return await j(e,t,n)}catch(r){if(r instanceof Error){s.logEsm(3,"resolveDirectory error",{specifier:e,context:t,error:r});const a=r;if(a.code==="ERR_UNSUPPORTED_DIR_IMPORT"){const i=U(a);if(i)try{return await w(`${i}/index`,t,n,!0)}catch(c){const o=c,{message:d}=o;throw o.message=o.message.replace(`${"/index".replace("/",p.sep)}'`,"'"),o.stack=o.stack.replace(d,o.message),o}}}throw r}},"resolveDirectory"),D=l(async(e,t,n)=>{if(s.logEsm(3,"resolveTsPaths",{specifier:e,context:t,requestAcceptsQuery:s.requestAcceptsQuery(e),tsconfigPathsMatcher:s.tsconfigPathsMatcher,fromNodeModules:t.parentURL?.includes("/node_modules/")}),!s.requestAcceptsQuery(e)&&s.tsconfigPathsMatcher&&!t.parentURL?.includes("/node_modules/")){const r=s.tsconfigPathsMatcher(e);s.logEsm(3,"resolveTsPaths",{possiblePaths:r});for(const a of r)try{return await q(g.pathToFileURL(a).toString(),t,n)}catch{}}return q(e,t,n)},"resolveTsPaths"),k="tsx://";if(exports.resolve=async(e,t,n)=>{if(!u.active||e.startsWith("node:"))return n(e,t);let r=y(e)??(t.parentURL&&y(t.parentURL));if(u.namespace){let o;if(e.startsWith(k)){try{o=JSON.parse(e.slice(k.length))}catch{}o?.namespace&&(r=o.namespace)}if(u.namespace!==r)return n(e,t);o&&(e=o.specifier,t.parentURL=o.parentURL)}const[a,i]=e.split("?"),c=await D(a,t,n);return s.logEsm(2,"nextResolve",{resolved:c}),c.format==="builtin"||(!c.format&&c.url.startsWith(s.fileUrlPrefix)&&(c.format=await W(c.url),s.logEsm(2,"getFormatFromFileUrl",{resolved:c,format:c.format})),i&&(c.url+=`?${i}`),r&&!c.url.includes(P)&&(c.url+=(c.url.includes("?")?"&":"?")+P+r)),c},s.debugEnabled){const e=exports.resolve;exports.resolve=async(t,n,r)=>{s.logEsm(2,"resolve",{specifier:t,context:n});const a=await e(t,n,r);return s.logEsm(1,"resolved",{specifier:t,context:n,result:a}),a}}m.isFeatureSupported(m.moduleRegister)&&_.isMainThread&&F.register(),exports.globalPreload=J,exports.initialize=b;
