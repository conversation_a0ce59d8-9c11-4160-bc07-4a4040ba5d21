{"name": "@types/mocha", "version": "10.0.10", "description": "TypeScript definitions for mocha", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/mocha", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "githubUsername": "kazimanzurrashid", "url": "https://github.com/kazimanzurrashid"}, {"name": "otiai10", "githubUsername": "otiai10", "url": "https://github.com/otiai10"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "enlight", "url": "https://github.com/enlight"}, {"name": "<PERSON>", "githubUsername": "cspotcode", "url": "https://github.com/cspotcode"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "1999", "url": "https://github.com/1999"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/strangedev"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/nicojs"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/mocha"}, "scripts": {}, "dependencies": {}, "peerDependencies": {}, "typesPublisherContentHash": "b89ce84325cff5100648bef2c908a3b012b62e7c60f1913704f0ee25be7cc888", "typeScriptVersion": "4.9"}