"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const resourceController_1 = __importDefault(require("../controllers/resourceController"));
const router = express_1.default.Router();
// GET /api/resources - Get all resources
router.get('/', resourceController_1.default.getAllResources);
// GET /api/resources/:id - Get resource by ID
router.get('/:id', resourceController_1.default.getResourceById);
// POST /api/resources - Create new resource
router.post('/', resourceController_1.default.createResource);
// PUT /api/resources/:id - Update resource (replace entire resource)
router.put('/:id', resourceController_1.default.updateResource);
// PATCH /api/resources/:id - Patch resource (partial update)
router.patch('/:id', resourceController_1.default.patchResource);
// DELETE /api/resources/:id - Delete resource
router.delete('/:id', resourceController_1.default.deleteResource);
exports.default = router;
//# sourceMappingURL=resourceRoutes.js.map