{"name": "yn", "version": "2.0.0", "description": "Parse yes/no like values", "license": "MIT", "repository": "sindresorhus/yn", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js", "lenient.js"], "keywords": ["yn", "yes", "no", "cli", "prompt", "validate", "input", "answer", "true", "false", "parse", "lenient"], "devDependencies": {"ava": "*", "xo": "*"}}